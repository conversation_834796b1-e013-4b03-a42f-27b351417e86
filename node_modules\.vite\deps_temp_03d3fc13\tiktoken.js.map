{"version": 3, "sources": ["../../tiktoken/tiktoken.js", "../../tiktoken/tiktoken_bg.js"], "sourcesContent": ["import * as wasm from \"./tiktoken_bg.wasm\";\nexport * from \"./tiktoken_bg.js\";\nimport { __wbg_set_wasm } from \"./tiktoken_bg.js\";\n__wbg_set_wasm(wasm);", "let wasm;\nexport function __wbg_set_wasm(val) {\n    wasm = val;\n}\n\n\nconst lTextDecoder = typeof TextDecoder === 'undefined' ? (0, module.require)('util').TextDecoder : TextDecoder;\n\nlet cachedTextDecoder = new lTextDecoder('utf-8', { ignoreBOM: true, fatal: true });\n\ncachedTextDecoder.decode();\n\nlet cachedUint8ArrayMemory0 = null;\n\nfunction getUint8ArrayMemory0() {\n    if (cachedUint8ArrayMemory0 === null || cachedUint8ArrayMemory0.byteLength === 0) {\n        cachedUint8ArrayMemory0 = new Uint8Array(wasm.memory.buffer);\n    }\n    return cachedUint8ArrayMemory0;\n}\n\nfunction getStringFromWasm0(ptr, len) {\n    ptr = ptr >>> 0;\n    return cachedTextDecoder.decode(getUint8ArrayMemory0().subarray(ptr, ptr + len));\n}\n\nconst heap = new Array(128).fill(undefined);\n\nheap.push(undefined, null, true, false);\n\nlet heap_next = heap.length;\n\nfunction addHeapObject(obj) {\n    if (heap_next === heap.length) heap.push(heap.length + 1);\n    const idx = heap_next;\n    heap_next = heap[idx];\n\n    heap[idx] = obj;\n    return idx;\n}\n\nfunction handleError(f, args) {\n    try {\n        return f.apply(this, args);\n    } catch (e) {\n        wasm.__wbindgen_export_0(addHeapObject(e));\n    }\n}\n\nfunction getObject(idx) { return heap[idx]; }\n\nfunction dropObject(idx) {\n    if (idx < 132) return;\n    heap[idx] = heap_next;\n    heap_next = idx;\n}\n\nfunction takeObject(idx) {\n    const ret = getObject(idx);\n    dropObject(idx);\n    return ret;\n}\n\nlet WASM_VECTOR_LEN = 0;\n\nconst lTextEncoder = typeof TextEncoder === 'undefined' ? (0, module.require)('util').TextEncoder : TextEncoder;\n\nlet cachedTextEncoder = new lTextEncoder('utf-8');\n\nconst encodeString = (typeof cachedTextEncoder.encodeInto === 'function'\n    ? function (arg, view) {\n    return cachedTextEncoder.encodeInto(arg, view);\n}\n    : function (arg, view) {\n    const buf = cachedTextEncoder.encode(arg);\n    view.set(buf);\n    return {\n        read: arg.length,\n        written: buf.length\n    };\n});\n\nfunction passStringToWasm0(arg, malloc, realloc) {\n\n    if (realloc === undefined) {\n        const buf = cachedTextEncoder.encode(arg);\n        const ptr = malloc(buf.length, 1) >>> 0;\n        getUint8ArrayMemory0().subarray(ptr, ptr + buf.length).set(buf);\n        WASM_VECTOR_LEN = buf.length;\n        return ptr;\n    }\n\n    let len = arg.length;\n    let ptr = malloc(len, 1) >>> 0;\n\n    const mem = getUint8ArrayMemory0();\n\n    let offset = 0;\n\n    for (; offset < len; offset++) {\n        const code = arg.charCodeAt(offset);\n        if (code > 0x7F) break;\n        mem[ptr + offset] = code;\n    }\n\n    if (offset !== len) {\n        if (offset !== 0) {\n            arg = arg.slice(offset);\n        }\n        ptr = realloc(ptr, len, len = offset + arg.length * 3, 1) >>> 0;\n        const view = getUint8ArrayMemory0().subarray(ptr + offset, ptr + len);\n        const ret = encodeString(arg, view);\n\n        offset += ret.written;\n        ptr = realloc(ptr, len, offset, 1) >>> 0;\n    }\n\n    WASM_VECTOR_LEN = offset;\n    return ptr;\n}\n\nfunction isLikeNone(x) {\n    return x === undefined || x === null;\n}\n\nlet cachedDataViewMemory0 = null;\n\nfunction getDataViewMemory0() {\n    if (cachedDataViewMemory0 === null || cachedDataViewMemory0.buffer.detached === true || (cachedDataViewMemory0.buffer.detached === undefined && cachedDataViewMemory0.buffer !== wasm.memory.buffer)) {\n        cachedDataViewMemory0 = new DataView(wasm.memory.buffer);\n    }\n    return cachedDataViewMemory0;\n}\n\nlet cachedUint32ArrayMemory0 = null;\n\nfunction getUint32ArrayMemory0() {\n    if (cachedUint32ArrayMemory0 === null || cachedUint32ArrayMemory0.byteLength === 0) {\n        cachedUint32ArrayMemory0 = new Uint32Array(wasm.memory.buffer);\n    }\n    return cachedUint32ArrayMemory0;\n}\n\nfunction getArrayU32FromWasm0(ptr, len) {\n    ptr = ptr >>> 0;\n    return getUint32ArrayMemory0().subarray(ptr / 4, ptr / 4 + len);\n}\n\nfunction passArray8ToWasm0(arg, malloc) {\n    const ptr = malloc(arg.length * 1, 1) >>> 0;\n    getUint8ArrayMemory0().set(arg, ptr / 1);\n    WASM_VECTOR_LEN = arg.length;\n    return ptr;\n}\n\nfunction passArray32ToWasm0(arg, malloc) {\n    const ptr = malloc(arg.length * 4, 4) >>> 0;\n    getUint32ArrayMemory0().set(arg, ptr / 4);\n    WASM_VECTOR_LEN = arg.length;\n    return ptr;\n}\n\nfunction getArrayU8FromWasm0(ptr, len) {\n    ptr = ptr >>> 0;\n    return getUint8ArrayMemory0().subarray(ptr / 1, ptr / 1 + len);\n}\n/**\n * @param {string} encoding\n * @param {any} extend_special_tokens\n * @returns {Tiktoken}\n */\nexport function get_encoding(encoding, extend_special_tokens) {\n    if (wasm == null) throw new Error(\"tiktoken: WASM binary has not been propery initialized.\");\n    try {\n        const retptr = wasm.__wbindgen_add_to_stack_pointer(-16);\n        const ptr0 = passStringToWasm0(encoding, wasm.__wbindgen_export_1, wasm.__wbindgen_export_2);\n        const len0 = WASM_VECTOR_LEN;\n        wasm.get_encoding(retptr, ptr0, len0, addHeapObject(extend_special_tokens));\n        var r0 = getDataViewMemory0().getInt32(retptr + 4 * 0, true);\n        var r1 = getDataViewMemory0().getInt32(retptr + 4 * 1, true);\n        var r2 = getDataViewMemory0().getInt32(retptr + 4 * 2, true);\n        if (r2) {\n            throw takeObject(r1);\n        }\n        return Tiktoken.__wrap(r0);\n    } finally {\n        wasm.__wbindgen_add_to_stack_pointer(16);\n    }\n}\n\n/**\n * @param {string} model\n * @param {any} extend_special_tokens\n * @returns {Tiktoken}\n */\nexport function encoding_for_model(model, extend_special_tokens) {\n    if (wasm == null) throw new Error(\"tiktoken: WASM binary has not been propery initialized.\");\n    try {\n        const retptr = wasm.__wbindgen_add_to_stack_pointer(-16);\n        const ptr0 = passStringToWasm0(model, wasm.__wbindgen_export_1, wasm.__wbindgen_export_2);\n        const len0 = WASM_VECTOR_LEN;\n        wasm.encoding_for_model(retptr, ptr0, len0, addHeapObject(extend_special_tokens));\n        var r0 = getDataViewMemory0().getInt32(retptr + 4 * 0, true);\n        var r1 = getDataViewMemory0().getInt32(retptr + 4 * 1, true);\n        var r2 = getDataViewMemory0().getInt32(retptr + 4 * 2, true);\n        if (r2) {\n            throw takeObject(r1);\n        }\n        return Tiktoken.__wrap(r0);\n    } finally {\n        wasm.__wbindgen_add_to_stack_pointer(16);\n    }\n}\n\n/**\n * @param {string} model\n * @returns {string}\n */\nexport function get_encoding_name_for_model(model) {\n    if (wasm == null) throw new Error(\"tiktoken: WASM binary has not been propery initialized.\");\n    let deferred3_0;\n    let deferred3_1;\n    try {\n        const retptr = wasm.__wbindgen_add_to_stack_pointer(-16);\n        const ptr0 = passStringToWasm0(model, wasm.__wbindgen_export_1, wasm.__wbindgen_export_2);\n        const len0 = WASM_VECTOR_LEN;\n        wasm.get_encoding_name_for_model(retptr, ptr0, len0);\n        var r0 = getDataViewMemory0().getInt32(retptr + 4 * 0, true);\n        var r1 = getDataViewMemory0().getInt32(retptr + 4 * 1, true);\n        var r2 = getDataViewMemory0().getInt32(retptr + 4 * 2, true);\n        var r3 = getDataViewMemory0().getInt32(retptr + 4 * 3, true);\n        var ptr2 = r0;\n        var len2 = r1;\n        if (r3) {\n            ptr2 = 0; len2 = 0;\n            throw takeObject(r2);\n        }\n        deferred3_0 = ptr2;\n        deferred3_1 = len2;\n        return getStringFromWasm0(ptr2, len2);\n    } finally {\n        wasm.__wbindgen_add_to_stack_pointer(16);\n        wasm.__wbindgen_export_3(deferred3_0, deferred3_1, 1);\n    }\n}\n\nconst TiktokenFinalization = (typeof FinalizationRegistry === 'undefined')\n    ? { register: () => {}, unregister: () => {} }\n    : new FinalizationRegistry(ptr => wasm.__wbg_tiktoken_free(ptr >>> 0, 1));\n\nexport class Tiktoken {\n\n    static __wrap(ptr) {\n        ptr = ptr >>> 0;\n        const obj = Object.create(Tiktoken.prototype);\n        obj.__wbg_ptr = ptr;\n        TiktokenFinalization.register(obj, obj.__wbg_ptr, obj);\n        return obj;\n    }\n\n    __destroy_into_raw() {\n        const ptr = this.__wbg_ptr;\n        this.__wbg_ptr = 0;\n        TiktokenFinalization.unregister(this);\n        return ptr;\n    }\n\n    free() {\n        if (wasm == null) throw new Error(\"tiktoken: WASM binary has not been propery initialized.\");\n        const ptr = this.__destroy_into_raw();\n        wasm.__wbg_tiktoken_free(ptr, 0);\n    }\n    /**\n     * @param {string} tiktoken_bfe\n     * @param {any} special_tokens\n     * @param {string} pat_str\n     */\n    constructor(tiktoken_bfe, special_tokens, pat_str) {\n        if (wasm == null) throw new Error(\"tiktoken: WASM binary has not been propery initialized.\");\n        const ptr0 = passStringToWasm0(tiktoken_bfe, wasm.__wbindgen_export_1, wasm.__wbindgen_export_2);\n        const len0 = WASM_VECTOR_LEN;\n        const ptr1 = passStringToWasm0(pat_str, wasm.__wbindgen_export_1, wasm.__wbindgen_export_2);\n        const len1 = WASM_VECTOR_LEN;\n        const ret = wasm.tiktoken_new(ptr0, len0, addHeapObject(special_tokens), ptr1, len1);\n        this.__wbg_ptr = ret >>> 0;\n        TiktokenFinalization.register(this, this.__wbg_ptr, this);\n        return this;\n    }\n    /**\n     * @returns {string | undefined}\n     */\n    get name() {\n        try {\n            const retptr = wasm.__wbindgen_add_to_stack_pointer(-16);\n            wasm.tiktoken_name(retptr, this.__wbg_ptr);\n            var r0 = getDataViewMemory0().getInt32(retptr + 4 * 0, true);\n            var r1 = getDataViewMemory0().getInt32(retptr + 4 * 1, true);\n            let v1;\n            if (r0 !== 0) {\n                v1 = getStringFromWasm0(r0, r1).slice();\n                wasm.__wbindgen_export_3(r0, r1 * 1, 1);\n            }\n            return v1;\n        } finally {\n            wasm.__wbindgen_add_to_stack_pointer(16);\n        }\n    }\n    /**\n     * @param {string} text\n     * @param {any} allowed_special\n     * @param {any} disallowed_special\n     * @returns {Uint32Array}\n     */\n    encode(text, allowed_special, disallowed_special) {\n        if (wasm == null) throw new Error(\"tiktoken: WASM binary has not been propery initialized.\");\n        try {\n            const retptr = wasm.__wbindgen_add_to_stack_pointer(-16);\n            const ptr0 = passStringToWasm0(text, wasm.__wbindgen_export_1, wasm.__wbindgen_export_2);\n            const len0 = WASM_VECTOR_LEN;\n            wasm.tiktoken_encode(retptr, this.__wbg_ptr, ptr0, len0, addHeapObject(allowed_special), addHeapObject(disallowed_special));\n            var r0 = getDataViewMemory0().getInt32(retptr + 4 * 0, true);\n            var r1 = getDataViewMemory0().getInt32(retptr + 4 * 1, true);\n            var r2 = getDataViewMemory0().getInt32(retptr + 4 * 2, true);\n            var r3 = getDataViewMemory0().getInt32(retptr + 4 * 3, true);\n            if (r3) {\n                throw takeObject(r2);\n            }\n            var v2 = getArrayU32FromWasm0(r0, r1).slice();\n            wasm.__wbindgen_export_3(r0, r1 * 4, 4);\n            return v2;\n        } finally {\n            wasm.__wbindgen_add_to_stack_pointer(16);\n        }\n    }\n    /**\n     * @param {string} text\n     * @returns {Uint32Array}\n     */\n    encode_ordinary(text) {\n        if (wasm == null) throw new Error(\"tiktoken: WASM binary has not been propery initialized.\");\n        try {\n            const retptr = wasm.__wbindgen_add_to_stack_pointer(-16);\n            const ptr0 = passStringToWasm0(text, wasm.__wbindgen_export_1, wasm.__wbindgen_export_2);\n            const len0 = WASM_VECTOR_LEN;\n            wasm.tiktoken_encode_ordinary(retptr, this.__wbg_ptr, ptr0, len0);\n            var r0 = getDataViewMemory0().getInt32(retptr + 4 * 0, true);\n            var r1 = getDataViewMemory0().getInt32(retptr + 4 * 1, true);\n            var v2 = getArrayU32FromWasm0(r0, r1).slice();\n            wasm.__wbindgen_export_3(r0, r1 * 4, 4);\n            return v2;\n        } finally {\n            wasm.__wbindgen_add_to_stack_pointer(16);\n        }\n    }\n    /**\n     * @param {string} text\n     * @param {any} allowed_special\n     * @param {any} disallowed_special\n     * @returns {any}\n     */\n    encode_with_unstable(text, allowed_special, disallowed_special) {\n        if (wasm == null) throw new Error(\"tiktoken: WASM binary has not been propery initialized.\");\n        try {\n            const retptr = wasm.__wbindgen_add_to_stack_pointer(-16);\n            const ptr0 = passStringToWasm0(text, wasm.__wbindgen_export_1, wasm.__wbindgen_export_2);\n            const len0 = WASM_VECTOR_LEN;\n            wasm.tiktoken_encode_with_unstable(retptr, this.__wbg_ptr, ptr0, len0, addHeapObject(allowed_special), addHeapObject(disallowed_special));\n            var r0 = getDataViewMemory0().getInt32(retptr + 4 * 0, true);\n            var r1 = getDataViewMemory0().getInt32(retptr + 4 * 1, true);\n            var r2 = getDataViewMemory0().getInt32(retptr + 4 * 2, true);\n            if (r2) {\n                throw takeObject(r1);\n            }\n            return takeObject(r0);\n        } finally {\n            wasm.__wbindgen_add_to_stack_pointer(16);\n        }\n    }\n    /**\n     * @param {Uint8Array} bytes\n     * @returns {number}\n     */\n    encode_single_token(bytes) {\n        if (wasm == null) throw new Error(\"tiktoken: WASM binary has not been propery initialized.\");\n        const ptr0 = passArray8ToWasm0(bytes, wasm.__wbindgen_export_1);\n        const len0 = WASM_VECTOR_LEN;\n        const ret = wasm.tiktoken_encode_single_token(this.__wbg_ptr, ptr0, len0);\n        return ret >>> 0;\n    }\n    /**\n     * @param {Uint32Array} tokens\n     * @returns {Uint8Array}\n     */\n    decode(tokens) {\n        if (wasm == null) throw new Error(\"tiktoken: WASM binary has not been propery initialized.\");\n        try {\n            const retptr = wasm.__wbindgen_add_to_stack_pointer(-16);\n            const ptr0 = passArray32ToWasm0(tokens, wasm.__wbindgen_export_1);\n            const len0 = WASM_VECTOR_LEN;\n            wasm.tiktoken_decode(retptr, this.__wbg_ptr, ptr0, len0);\n            var r0 = getDataViewMemory0().getInt32(retptr + 4 * 0, true);\n            var r1 = getDataViewMemory0().getInt32(retptr + 4 * 1, true);\n            var v2 = getArrayU8FromWasm0(r0, r1).slice();\n            wasm.__wbindgen_export_3(r0, r1 * 1, 1);\n            return v2;\n        } finally {\n            wasm.__wbindgen_add_to_stack_pointer(16);\n        }\n    }\n    /**\n     * @param {number} token\n     * @returns {Uint8Array}\n     */\n    decode_single_token_bytes(token) {\n        if (wasm == null) throw new Error(\"tiktoken: WASM binary has not been propery initialized.\");\n        try {\n            const retptr = wasm.__wbindgen_add_to_stack_pointer(-16);\n            wasm.tiktoken_decode_single_token_bytes(retptr, this.__wbg_ptr, token);\n            var r0 = getDataViewMemory0().getInt32(retptr + 4 * 0, true);\n            var r1 = getDataViewMemory0().getInt32(retptr + 4 * 1, true);\n            var v1 = getArrayU8FromWasm0(r0, r1).slice();\n            wasm.__wbindgen_export_3(r0, r1 * 1, 1);\n            return v1;\n        } finally {\n            wasm.__wbindgen_add_to_stack_pointer(16);\n        }\n    }\n    /**\n     * @returns {any}\n     */\n    token_byte_values() {\n        if (wasm == null) throw new Error(\"tiktoken: WASM binary has not been propery initialized.\");\n        const ret = wasm.tiktoken_token_byte_values(this.__wbg_ptr);\n        return takeObject(ret);\n    }\n}\n\nexport function __wbg_parse_def2e24ef1252aff() { return handleError(function (arg0, arg1) {\n    const ret = JSON.parse(getStringFromWasm0(arg0, arg1));\n    return addHeapObject(ret);\n}, arguments) };\n\nexport function __wbg_stringify_f7ed6987935b4a24() { return handleError(function (arg0) {\n    const ret = JSON.stringify(getObject(arg0));\n    return addHeapObject(ret);\n}, arguments) };\n\nexport function __wbindgen_error_new(arg0, arg1) {\n    const ret = new Error(getStringFromWasm0(arg0, arg1));\n    return addHeapObject(ret);\n};\n\nexport function __wbindgen_is_undefined(arg0) {\n    const ret = getObject(arg0) === undefined;\n    return ret;\n};\n\nexport function __wbindgen_object_drop_ref(arg0) {\n    takeObject(arg0);\n};\n\nexport function __wbindgen_string_get(arg0, arg1) {\n    if (wasm == null) throw new Error(\"tiktoken: WASM binary has not been propery initialized.\");\n    const obj = getObject(arg1);\n    const ret = typeof(obj) === 'string' ? obj : undefined;\n    var ptr1 = isLikeNone(ret) ? 0 : passStringToWasm0(ret, wasm.__wbindgen_export_1, wasm.__wbindgen_export_2);\n    var len1 = WASM_VECTOR_LEN;\n    getDataViewMemory0().setInt32(arg0 + 4 * 1, len1, true);\n    getDataViewMemory0().setInt32(arg0 + 4 * 0, ptr1, true);\n};\n\nexport function __wbindgen_throw(arg0, arg1) {\n    throw new Error(getStringFromWasm0(arg0, arg1));\n};\n\n"], "mappings": ";;;AAAA,YAAYA,WAAU;;;ACAtB,IAAI;AACG,SAAS,eAAe,KAAK;AAChC,SAAO;AACX;AAGA,IAAM,eAAe,OAAO,gBAAgB,eAAe,GAAG,OAAO,SAAS,MAAM,EAAE,cAAc;AAEpG,IAAI,oBAAoB,IAAI,aAAa,SAAS,EAAE,WAAW,MAAM,OAAO,KAAK,CAAC;AAElF,kBAAkB,OAAO;AAEzB,IAAI,0BAA0B;AAE9B,SAAS,uBAAuB;AAC5B,MAAI,4BAA4B,QAAQ,wBAAwB,eAAe,GAAG;AAC9E,8BAA0B,IAAI,WAAW,KAAK,OAAO,MAAM;AAAA,EAC/D;AACA,SAAO;AACX;AAEA,SAAS,mBAAmB,KAAK,KAAK;AAClC,QAAM,QAAQ;AACd,SAAO,kBAAkB,OAAO,qBAAqB,EAAE,SAAS,KAAK,MAAM,GAAG,CAAC;AACnF;AAEA,IAAM,OAAO,IAAI,MAAM,GAAG,EAAE,KAAK,MAAS;AAE1C,KAAK,KAAK,QAAW,MAAM,MAAM,KAAK;AAEtC,IAAI,YAAY,KAAK;AAErB,SAAS,cAAc,KAAK;AACxB,MAAI,cAAc,KAAK,OAAQ,MAAK,KAAK,KAAK,SAAS,CAAC;AACxD,QAAM,MAAM;AACZ,cAAY,KAAK,GAAG;AAEpB,OAAK,GAAG,IAAI;AACZ,SAAO;AACX;AAEA,SAAS,YAAY,GAAG,MAAM;AAC1B,MAAI;AACA,WAAO,EAAE,MAAM,MAAM,IAAI;AAAA,EAC7B,SAAS,GAAG;AACR,SAAK,oBAAoB,cAAc,CAAC,CAAC;AAAA,EAC7C;AACJ;AAEA,SAAS,UAAU,KAAK;AAAE,SAAO,KAAK,GAAG;AAAG;AAE5C,SAAS,WAAW,KAAK;AACrB,MAAI,MAAM,IAAK;AACf,OAAK,GAAG,IAAI;AACZ,cAAY;AAChB;AAEA,SAAS,WAAW,KAAK;AACrB,QAAM,MAAM,UAAU,GAAG;AACzB,aAAW,GAAG;AACd,SAAO;AACX;AAEA,IAAI,kBAAkB;AAEtB,IAAM,eAAe,OAAO,gBAAgB,eAAe,GAAG,OAAO,SAAS,MAAM,EAAE,cAAc;AAEpG,IAAI,oBAAoB,IAAI,aAAa,OAAO;AAEhD,IAAM,eAAgB,OAAO,kBAAkB,eAAe,aACxD,SAAU,KAAK,MAAM;AACvB,SAAO,kBAAkB,WAAW,KAAK,IAAI;AACjD,IACM,SAAU,KAAK,MAAM;AACvB,QAAM,MAAM,kBAAkB,OAAO,GAAG;AACxC,OAAK,IAAI,GAAG;AACZ,SAAO;AAAA,IACH,MAAM,IAAI;AAAA,IACV,SAAS,IAAI;AAAA,EACjB;AACJ;AAEA,SAAS,kBAAkB,KAAK,QAAQ,SAAS;AAE7C,MAAI,YAAY,QAAW;AACvB,UAAM,MAAM,kBAAkB,OAAO,GAAG;AACxC,UAAMC,OAAM,OAAO,IAAI,QAAQ,CAAC,MAAM;AACtC,yBAAqB,EAAE,SAASA,MAAKA,OAAM,IAAI,MAAM,EAAE,IAAI,GAAG;AAC9D,sBAAkB,IAAI;AACtB,WAAOA;AAAA,EACX;AAEA,MAAI,MAAM,IAAI;AACd,MAAI,MAAM,OAAO,KAAK,CAAC,MAAM;AAE7B,QAAM,MAAM,qBAAqB;AAEjC,MAAI,SAAS;AAEb,SAAO,SAAS,KAAK,UAAU;AAC3B,UAAM,OAAO,IAAI,WAAW,MAAM;AAClC,QAAI,OAAO,IAAM;AACjB,QAAI,MAAM,MAAM,IAAI;AAAA,EACxB;AAEA,MAAI,WAAW,KAAK;AAChB,QAAI,WAAW,GAAG;AACd,YAAM,IAAI,MAAM,MAAM;AAAA,IAC1B;AACA,UAAM,QAAQ,KAAK,KAAK,MAAM,SAAS,IAAI,SAAS,GAAG,CAAC,MAAM;AAC9D,UAAM,OAAO,qBAAqB,EAAE,SAAS,MAAM,QAAQ,MAAM,GAAG;AACpE,UAAM,MAAM,aAAa,KAAK,IAAI;AAElC,cAAU,IAAI;AACd,UAAM,QAAQ,KAAK,KAAK,QAAQ,CAAC,MAAM;AAAA,EAC3C;AAEA,oBAAkB;AAClB,SAAO;AACX;AAEA,SAAS,WAAW,GAAG;AACnB,SAAO,MAAM,UAAa,MAAM;AACpC;AAEA,IAAI,wBAAwB;AAE5B,SAAS,qBAAqB;AAC1B,MAAI,0BAA0B,QAAQ,sBAAsB,OAAO,aAAa,QAAS,sBAAsB,OAAO,aAAa,UAAa,sBAAsB,WAAW,KAAK,OAAO,QAAS;AAClM,4BAAwB,IAAI,SAAS,KAAK,OAAO,MAAM;AAAA,EAC3D;AACA,SAAO;AACX;AAEA,IAAI,2BAA2B;AAE/B,SAAS,wBAAwB;AAC7B,MAAI,6BAA6B,QAAQ,yBAAyB,eAAe,GAAG;AAChF,+BAA2B,IAAI,YAAY,KAAK,OAAO,MAAM;AAAA,EACjE;AACA,SAAO;AACX;AAEA,SAAS,qBAAqB,KAAK,KAAK;AACpC,QAAM,QAAQ;AACd,SAAO,sBAAsB,EAAE,SAAS,MAAM,GAAG,MAAM,IAAI,GAAG;AAClE;AAEA,SAAS,kBAAkB,KAAK,QAAQ;AACpC,QAAM,MAAM,OAAO,IAAI,SAAS,GAAG,CAAC,MAAM;AAC1C,uBAAqB,EAAE,IAAI,KAAK,MAAM,CAAC;AACvC,oBAAkB,IAAI;AACtB,SAAO;AACX;AAEA,SAAS,mBAAmB,KAAK,QAAQ;AACrC,QAAM,MAAM,OAAO,IAAI,SAAS,GAAG,CAAC,MAAM;AAC1C,wBAAsB,EAAE,IAAI,KAAK,MAAM,CAAC;AACxC,oBAAkB,IAAI;AACtB,SAAO;AACX;AAEA,SAAS,oBAAoB,KAAK,KAAK;AACnC,QAAM,QAAQ;AACd,SAAO,qBAAqB,EAAE,SAAS,MAAM,GAAG,MAAM,IAAI,GAAG;AACjE;AAMO,SAAS,aAAa,UAAU,uBAAuB;AAC1D,MAAI,QAAQ,KAAM,OAAM,IAAI,MAAM,yDAAyD;AAC3F,MAAI;AACA,UAAM,SAAS,KAAK,gCAAgC,GAAG;AACvD,UAAM,OAAO,kBAAkB,UAAU,KAAK,qBAAqB,KAAK,mBAAmB;AAC3F,UAAM,OAAO;AACb,SAAK,aAAa,QAAQ,MAAM,MAAM,cAAc,qBAAqB,CAAC;AAC1E,QAAI,KAAK,mBAAmB,EAAE,SAAS,SAAS,IAAI,GAAG,IAAI;AAC3D,QAAI,KAAK,mBAAmB,EAAE,SAAS,SAAS,IAAI,GAAG,IAAI;AAC3D,QAAI,KAAK,mBAAmB,EAAE,SAAS,SAAS,IAAI,GAAG,IAAI;AAC3D,QAAI,IAAI;AACJ,YAAM,WAAW,EAAE;AAAA,IACvB;AACA,WAAO,SAAS,OAAO,EAAE;AAAA,EAC7B,UAAE;AACE,SAAK,gCAAgC,EAAE;AAAA,EAC3C;AACJ;AAOO,SAAS,mBAAmB,OAAO,uBAAuB;AAC7D,MAAI,QAAQ,KAAM,OAAM,IAAI,MAAM,yDAAyD;AAC3F,MAAI;AACA,UAAM,SAAS,KAAK,gCAAgC,GAAG;AACvD,UAAM,OAAO,kBAAkB,OAAO,KAAK,qBAAqB,KAAK,mBAAmB;AACxF,UAAM,OAAO;AACb,SAAK,mBAAmB,QAAQ,MAAM,MAAM,cAAc,qBAAqB,CAAC;AAChF,QAAI,KAAK,mBAAmB,EAAE,SAAS,SAAS,IAAI,GAAG,IAAI;AAC3D,QAAI,KAAK,mBAAmB,EAAE,SAAS,SAAS,IAAI,GAAG,IAAI;AAC3D,QAAI,KAAK,mBAAmB,EAAE,SAAS,SAAS,IAAI,GAAG,IAAI;AAC3D,QAAI,IAAI;AACJ,YAAM,WAAW,EAAE;AAAA,IACvB;AACA,WAAO,SAAS,OAAO,EAAE;AAAA,EAC7B,UAAE;AACE,SAAK,gCAAgC,EAAE;AAAA,EAC3C;AACJ;AAMO,SAAS,4BAA4B,OAAO;AAC/C,MAAI,QAAQ,KAAM,OAAM,IAAI,MAAM,yDAAyD;AAC3F,MAAI;AACJ,MAAI;AACJ,MAAI;AACA,UAAM,SAAS,KAAK,gCAAgC,GAAG;AACvD,UAAM,OAAO,kBAAkB,OAAO,KAAK,qBAAqB,KAAK,mBAAmB;AACxF,UAAM,OAAO;AACb,SAAK,4BAA4B,QAAQ,MAAM,IAAI;AACnD,QAAI,KAAK,mBAAmB,EAAE,SAAS,SAAS,IAAI,GAAG,IAAI;AAC3D,QAAI,KAAK,mBAAmB,EAAE,SAAS,SAAS,IAAI,GAAG,IAAI;AAC3D,QAAI,KAAK,mBAAmB,EAAE,SAAS,SAAS,IAAI,GAAG,IAAI;AAC3D,QAAI,KAAK,mBAAmB,EAAE,SAAS,SAAS,IAAI,GAAG,IAAI;AAC3D,QAAI,OAAO;AACX,QAAI,OAAO;AACX,QAAI,IAAI;AACJ,aAAO;AAAG,aAAO;AACjB,YAAM,WAAW,EAAE;AAAA,IACvB;AACA,kBAAc;AACd,kBAAc;AACd,WAAO,mBAAmB,MAAM,IAAI;AAAA,EACxC,UAAE;AACE,SAAK,gCAAgC,EAAE;AACvC,SAAK,oBAAoB,aAAa,aAAa,CAAC;AAAA,EACxD;AACJ;AAEA,IAAM,uBAAwB,OAAO,yBAAyB,cACxD,EAAE,UAAU,MAAM;AAAC,GAAG,YAAY,MAAM;AAAC,EAAE,IAC3C,IAAI,qBAAqB,SAAO,KAAK,oBAAoB,QAAQ,GAAG,CAAC,CAAC;AAErE,IAAM,WAAN,MAAM,UAAS;AAAA,EAElB,OAAO,OAAO,KAAK;AACf,UAAM,QAAQ;AACd,UAAM,MAAM,OAAO,OAAO,UAAS,SAAS;AAC5C,QAAI,YAAY;AAChB,yBAAqB,SAAS,KAAK,IAAI,WAAW,GAAG;AACrD,WAAO;AAAA,EACX;AAAA,EAEA,qBAAqB;AACjB,UAAM,MAAM,KAAK;AACjB,SAAK,YAAY;AACjB,yBAAqB,WAAW,IAAI;AACpC,WAAO;AAAA,EACX;AAAA,EAEA,OAAO;AACH,QAAI,QAAQ,KAAM,OAAM,IAAI,MAAM,yDAAyD;AAC3F,UAAM,MAAM,KAAK,mBAAmB;AACpC,SAAK,oBAAoB,KAAK,CAAC;AAAA,EACnC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,YAAY,cAAc,gBAAgB,SAAS;AAC/C,QAAI,QAAQ,KAAM,OAAM,IAAI,MAAM,yDAAyD;AAC3F,UAAM,OAAO,kBAAkB,cAAc,KAAK,qBAAqB,KAAK,mBAAmB;AAC/F,UAAM,OAAO;AACb,UAAM,OAAO,kBAAkB,SAAS,KAAK,qBAAqB,KAAK,mBAAmB;AAC1F,UAAM,OAAO;AACb,UAAM,MAAM,KAAK,aAAa,MAAM,MAAM,cAAc,cAAc,GAAG,MAAM,IAAI;AACnF,SAAK,YAAY,QAAQ;AACzB,yBAAqB,SAAS,MAAM,KAAK,WAAW,IAAI;AACxD,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA,EAIA,IAAI,OAAO;AACP,QAAI;AACA,YAAM,SAAS,KAAK,gCAAgC,GAAG;AACvD,WAAK,cAAc,QAAQ,KAAK,SAAS;AACzC,UAAI,KAAK,mBAAmB,EAAE,SAAS,SAAS,IAAI,GAAG,IAAI;AAC3D,UAAI,KAAK,mBAAmB,EAAE,SAAS,SAAS,IAAI,GAAG,IAAI;AAC3D,UAAI;AACJ,UAAI,OAAO,GAAG;AACV,aAAK,mBAAmB,IAAI,EAAE,EAAE,MAAM;AACtC,aAAK,oBAAoB,IAAI,KAAK,GAAG,CAAC;AAAA,MAC1C;AACA,aAAO;AAAA,IACX,UAAE;AACE,WAAK,gCAAgC,EAAE;AAAA,IAC3C;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,OAAO,MAAM,iBAAiB,oBAAoB;AAC9C,QAAI,QAAQ,KAAM,OAAM,IAAI,MAAM,yDAAyD;AAC3F,QAAI;AACA,YAAM,SAAS,KAAK,gCAAgC,GAAG;AACvD,YAAM,OAAO,kBAAkB,MAAM,KAAK,qBAAqB,KAAK,mBAAmB;AACvF,YAAM,OAAO;AACb,WAAK,gBAAgB,QAAQ,KAAK,WAAW,MAAM,MAAM,cAAc,eAAe,GAAG,cAAc,kBAAkB,CAAC;AAC1H,UAAI,KAAK,mBAAmB,EAAE,SAAS,SAAS,IAAI,GAAG,IAAI;AAC3D,UAAI,KAAK,mBAAmB,EAAE,SAAS,SAAS,IAAI,GAAG,IAAI;AAC3D,UAAI,KAAK,mBAAmB,EAAE,SAAS,SAAS,IAAI,GAAG,IAAI;AAC3D,UAAI,KAAK,mBAAmB,EAAE,SAAS,SAAS,IAAI,GAAG,IAAI;AAC3D,UAAI,IAAI;AACJ,cAAM,WAAW,EAAE;AAAA,MACvB;AACA,UAAI,KAAK,qBAAqB,IAAI,EAAE,EAAE,MAAM;AAC5C,WAAK,oBAAoB,IAAI,KAAK,GAAG,CAAC;AACtC,aAAO;AAAA,IACX,UAAE;AACE,WAAK,gCAAgC,EAAE;AAAA,IAC3C;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,gBAAgB,MAAM;AAClB,QAAI,QAAQ,KAAM,OAAM,IAAI,MAAM,yDAAyD;AAC3F,QAAI;AACA,YAAM,SAAS,KAAK,gCAAgC,GAAG;AACvD,YAAM,OAAO,kBAAkB,MAAM,KAAK,qBAAqB,KAAK,mBAAmB;AACvF,YAAM,OAAO;AACb,WAAK,yBAAyB,QAAQ,KAAK,WAAW,MAAM,IAAI;AAChE,UAAI,KAAK,mBAAmB,EAAE,SAAS,SAAS,IAAI,GAAG,IAAI;AAC3D,UAAI,KAAK,mBAAmB,EAAE,SAAS,SAAS,IAAI,GAAG,IAAI;AAC3D,UAAI,KAAK,qBAAqB,IAAI,EAAE,EAAE,MAAM;AAC5C,WAAK,oBAAoB,IAAI,KAAK,GAAG,CAAC;AACtC,aAAO;AAAA,IACX,UAAE;AACE,WAAK,gCAAgC,EAAE;AAAA,IAC3C;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,qBAAqB,MAAM,iBAAiB,oBAAoB;AAC5D,QAAI,QAAQ,KAAM,OAAM,IAAI,MAAM,yDAAyD;AAC3F,QAAI;AACA,YAAM,SAAS,KAAK,gCAAgC,GAAG;AACvD,YAAM,OAAO,kBAAkB,MAAM,KAAK,qBAAqB,KAAK,mBAAmB;AACvF,YAAM,OAAO;AACb,WAAK,8BAA8B,QAAQ,KAAK,WAAW,MAAM,MAAM,cAAc,eAAe,GAAG,cAAc,kBAAkB,CAAC;AACxI,UAAI,KAAK,mBAAmB,EAAE,SAAS,SAAS,IAAI,GAAG,IAAI;AAC3D,UAAI,KAAK,mBAAmB,EAAE,SAAS,SAAS,IAAI,GAAG,IAAI;AAC3D,UAAI,KAAK,mBAAmB,EAAE,SAAS,SAAS,IAAI,GAAG,IAAI;AAC3D,UAAI,IAAI;AACJ,cAAM,WAAW,EAAE;AAAA,MACvB;AACA,aAAO,WAAW,EAAE;AAAA,IACxB,UAAE;AACE,WAAK,gCAAgC,EAAE;AAAA,IAC3C;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,oBAAoB,OAAO;AACvB,QAAI,QAAQ,KAAM,OAAM,IAAI,MAAM,yDAAyD;AAC3F,UAAM,OAAO,kBAAkB,OAAO,KAAK,mBAAmB;AAC9D,UAAM,OAAO;AACb,UAAM,MAAM,KAAK,6BAA6B,KAAK,WAAW,MAAM,IAAI;AACxE,WAAO,QAAQ;AAAA,EACnB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,OAAO,QAAQ;AACX,QAAI,QAAQ,KAAM,OAAM,IAAI,MAAM,yDAAyD;AAC3F,QAAI;AACA,YAAM,SAAS,KAAK,gCAAgC,GAAG;AACvD,YAAM,OAAO,mBAAmB,QAAQ,KAAK,mBAAmB;AAChE,YAAM,OAAO;AACb,WAAK,gBAAgB,QAAQ,KAAK,WAAW,MAAM,IAAI;AACvD,UAAI,KAAK,mBAAmB,EAAE,SAAS,SAAS,IAAI,GAAG,IAAI;AAC3D,UAAI,KAAK,mBAAmB,EAAE,SAAS,SAAS,IAAI,GAAG,IAAI;AAC3D,UAAI,KAAK,oBAAoB,IAAI,EAAE,EAAE,MAAM;AAC3C,WAAK,oBAAoB,IAAI,KAAK,GAAG,CAAC;AACtC,aAAO;AAAA,IACX,UAAE;AACE,WAAK,gCAAgC,EAAE;AAAA,IAC3C;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,0BAA0B,OAAO;AAC7B,QAAI,QAAQ,KAAM,OAAM,IAAI,MAAM,yDAAyD;AAC3F,QAAI;AACA,YAAM,SAAS,KAAK,gCAAgC,GAAG;AACvD,WAAK,mCAAmC,QAAQ,KAAK,WAAW,KAAK;AACrE,UAAI,KAAK,mBAAmB,EAAE,SAAS,SAAS,IAAI,GAAG,IAAI;AAC3D,UAAI,KAAK,mBAAmB,EAAE,SAAS,SAAS,IAAI,GAAG,IAAI;AAC3D,UAAI,KAAK,oBAAoB,IAAI,EAAE,EAAE,MAAM;AAC3C,WAAK,oBAAoB,IAAI,KAAK,GAAG,CAAC;AACtC,aAAO;AAAA,IACX,UAAE;AACE,WAAK,gCAAgC,EAAE;AAAA,IAC3C;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA,EAIA,oBAAoB;AAChB,QAAI,QAAQ,KAAM,OAAM,IAAI,MAAM,yDAAyD;AAC3F,UAAM,MAAM,KAAK,2BAA2B,KAAK,SAAS;AAC1D,WAAO,WAAW,GAAG;AAAA,EACzB;AACJ;AAEO,SAAS,+BAA+B;AAAE,SAAO,YAAY,SAAU,MAAM,MAAM;AACtF,UAAM,MAAM,KAAK,MAAM,mBAAmB,MAAM,IAAI,CAAC;AACrD,WAAO,cAAc,GAAG;AAAA,EAC5B,GAAG,SAAS;AAAE;AAEP,SAAS,mCAAmC;AAAE,SAAO,YAAY,SAAU,MAAM;AACpF,UAAM,MAAM,KAAK,UAAU,UAAU,IAAI,CAAC;AAC1C,WAAO,cAAc,GAAG;AAAA,EAC5B,GAAG,SAAS;AAAE;AAEP,SAAS,qBAAqB,MAAM,MAAM;AAC7C,QAAM,MAAM,IAAI,MAAM,mBAAmB,MAAM,IAAI,CAAC;AACpD,SAAO,cAAc,GAAG;AAC5B;AAEO,SAAS,wBAAwB,MAAM;AAC1C,QAAM,MAAM,UAAU,IAAI,MAAM;AAChC,SAAO;AACX;AAEO,SAAS,2BAA2B,MAAM;AAC7C,aAAW,IAAI;AACnB;AAEO,SAAS,sBAAsB,MAAM,MAAM;AAC9C,MAAI,QAAQ,KAAM,OAAM,IAAI,MAAM,yDAAyD;AAC3F,QAAM,MAAM,UAAU,IAAI;AAC1B,QAAM,MAAM,OAAO,QAAS,WAAW,MAAM;AAC7C,MAAI,OAAO,WAAW,GAAG,IAAI,IAAI,kBAAkB,KAAK,KAAK,qBAAqB,KAAK,mBAAmB;AAC1G,MAAI,OAAO;AACX,qBAAmB,EAAE,SAAS,OAAO,IAAI,GAAG,MAAM,IAAI;AACtD,qBAAmB,EAAE,SAAS,OAAO,IAAI,GAAG,MAAM,IAAI;AAC1D;AAEO,SAAS,iBAAiB,MAAM,MAAM;AACzC,QAAM,IAAI,MAAM,mBAAmB,MAAM,IAAI,CAAC;AAClD;;;ADtdA,eAAeC,KAAI;", "names": ["wasm", "ptr", "wasm"]}