// Integration tests for core functionality
describe('Arien Agent Integration Tests', () => {
  describe('Basic Functionality', () => {
    test('should have proper TypeScript types', () => {
      // Test that our types are properly defined
      const message = {
        id: 'test-1',
        type: 'user' as const,
        content: 'Hello',
        timestamp: new Date()
      };

      expect(message.id).toBe('test-1');
      expect(message.type).toBe('user');
      expect(message.content).toBe('Hello');
      expect(message.timestamp).toBeInstanceOf(Date);
    });

    test('should handle tool schema validation', () => {
      const schema = {
        type: 'object' as const,
        properties: {
          name: { type: 'string' as const },
          count: { type: 'number' as const }
        },
        required: ['name']
      };

      // Valid arguments
      const validArgs = { name: 'test', count: 5 };
      expect(typeof validArgs.name).toBe('string');
      expect(typeof validArgs.count).toBe('number');

      // Invalid arguments
      const invalidArgs = { count: 5 }; // Missing required 'name'
      expect('name' in invalidArgs).toBe(false);
    });

    test('should handle async operations', async () => {
      const asyncOperation = async (input: string): Promise<string> => {
        return new Promise((resolve) => {
          setTimeout(() => resolve(`Processed: ${input}`), 10);
        });
      };

      const result = await asyncOperation('test input');
      expect(result).toBe('Processed: test input');
    });

    test('should handle error scenarios', () => {
      const errorFunction = () => {
        throw new Error('Test error');
      };

      expect(() => errorFunction()).toThrow('Test error');
    });

    test('should validate configuration objects', () => {
      const config = {
        provider: 'openai',
        model: 'gpt-4o',
        apiKey: 'test-key',
        maxTokens: 1000,
        temperature: 0.7
      };

      // Validate required fields
      expect(config.provider).toBeDefined();
      expect(config.model).toBeDefined();
      expect(config.apiKey).toBeDefined();
      
      // Validate types
      expect(typeof config.maxTokens).toBe('number');
      expect(typeof config.temperature).toBe('number');
      
      // Validate ranges
      expect(config.temperature).toBeGreaterThanOrEqual(0);
      expect(config.temperature).toBeLessThanOrEqual(2);
      expect(config.maxTokens).toBeGreaterThan(0);
    });

    test('should handle JSON serialization', () => {
      const data = {
        id: 'test-123',
        timestamp: new Date().toISOString(),
        metadata: {
          source: 'test',
          version: '1.0.0'
        }
      };

      const serialized = JSON.stringify(data);
      const deserialized = JSON.parse(serialized);

      expect(deserialized.id).toBe(data.id);
      expect(deserialized.timestamp).toBe(data.timestamp);
      expect(deserialized.metadata.source).toBe(data.metadata.source);
    });

    test('should handle array operations', () => {
      const items = ['item1', 'item2', 'item3'];
      
      // Test array methods
      expect(items.length).toBe(3);
      expect(items.includes('item2')).toBe(true);
      expect(items.indexOf('item3')).toBe(2);
      
      // Test filtering
      const filtered = items.filter(item => item.includes('2'));
      expect(filtered).toHaveLength(1);
      expect(filtered[0]).toBe('item2');
      
      // Test mapping
      const mapped = items.map(item => item.toUpperCase());
      expect(mapped).toEqual(['ITEM1', 'ITEM2', 'ITEM3']);
    });

    test('should handle string operations', () => {
      const text = 'Hello, World!';
      
      expect(text.toLowerCase()).toBe('hello, world!');
      expect(text.includes('World')).toBe(true);
      expect(text.split(', ')).toEqual(['Hello', 'World!']);
      expect(text.replace('World', 'Universe')).toBe('Hello, Universe!');
    });

    test('should handle date operations', () => {
      const now = new Date();
      const timestamp = now.getTime();
      const isoString = now.toISOString();
      
      expect(typeof timestamp).toBe('number');
      expect(typeof isoString).toBe('string');
      expect(new Date(timestamp).getTime()).toBe(timestamp);
      expect(new Date(isoString).toISOString()).toBe(isoString);
    });

    test('should handle promise chains', async () => {
      const step1 = (input: string) => Promise.resolve(`Step1: ${input}`);
      const step2 = (input: string) => Promise.resolve(`Step2: ${input}`);
      const step3 = (input: string) => Promise.resolve(`Step3: ${input}`);

      const result = await step1('start')
        .then(step2)
        .then(step3);

      expect(result).toBe('Step3: Step2: Step1: start');
    });
  });

  describe('Error Handling', () => {
    test('should handle network-like errors', async () => {
      const networkCall = async (shouldFail: boolean): Promise<string> => {
        if (shouldFail) {
          throw new Error('Network error');
        }
        return 'Success';
      };

      // Test success case
      const success = await networkCall(false);
      expect(success).toBe('Success');

      // Test error case
      await expect(networkCall(true)).rejects.toThrow('Network error');
    });

    test('should handle validation errors', () => {
      const validate = (data: any): { valid: boolean; errors: string[] } => {
        const errors: string[] = [];
        
        if (!data.name) errors.push('Name is required');
        if (!data.email) errors.push('Email is required');
        if (data.age && data.age < 0) errors.push('Age must be positive');
        
        return {
          valid: errors.length === 0,
          errors
        };
      };

      // Valid data
      const validResult = validate({ name: 'John', email: '<EMAIL>', age: 25 });
      expect(validResult.valid).toBe(true);
      expect(validResult.errors).toHaveLength(0);

      // Invalid data
      const invalidResult = validate({ age: -5 });
      expect(invalidResult.valid).toBe(false);
      expect(invalidResult.errors).toContain('Name is required');
      expect(invalidResult.errors).toContain('Email is required');
      expect(invalidResult.errors).toContain('Age must be positive');
    });
  });

  describe('Performance', () => {
    test('should handle large arrays efficiently', () => {
      const largeArray = Array.from({ length: 10000 }, (_, i) => i);
      
      const start = Date.now();
      const filtered = largeArray.filter(n => n % 2 === 0);
      const end = Date.now();
      
      expect(filtered.length).toBe(5000);
      expect(end - start).toBeLessThan(100); // Should complete in under 100ms
    });

    test('should handle concurrent operations', async () => {
      const asyncTask = (id: number, delay: number) => 
        new Promise(resolve => setTimeout(() => resolve(`Task ${id}`), delay));

      const start = Date.now();
      const results = await Promise.all([
        asyncTask(1, 10),
        asyncTask(2, 20),
        asyncTask(3, 15)
      ]);
      const end = Date.now();

      expect(results).toEqual(['Task 1', 'Task 2', 'Task 3']);
      expect(end - start).toBeLessThan(50); // Should complete concurrently
    });
  });
});
