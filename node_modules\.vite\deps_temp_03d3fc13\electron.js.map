{"version": 3, "sources": ["../../electron/index.js"], "sourcesContent": ["const fs = require('fs');\nconst path = require('path');\n\nconst pathFile = path.join(__dirname, 'path.txt');\n\nfunction getElectronPath () {\n  let executablePath;\n  if (fs.existsSync(pathFile)) {\n    executablePath = fs.readFileSync(pathFile, 'utf-8');\n  }\n  if (process.env.ELECTRON_OVERRIDE_DIST_PATH) {\n    return path.join(process.env.ELECTRON_OVERRIDE_DIST_PATH, executablePath || 'electron');\n  }\n  if (executablePath) {\n    return path.join(__dirname, 'dist', executablePath);\n  } else {\n    throw new Error('Electron failed to install correctly, please delete node_modules/electron and try installing again');\n  }\n}\n\nmodule.exports = getElectronPath();\n"], "mappings": ";;;;;;;;;AAAA;AAAA;AAAA,QAAM,KAAK;AACX,QAAM,OAAO;AAEb,QAAM,WAAW,KAAK,KAAK,WAAW,UAAU;AAEhD,aAAS,kBAAmB;AAC1B,UAAI;AACJ,UAAI,GAAG,WAAW,QAAQ,GAAG;AAC3B,yBAAiB,GAAG,aAAa,UAAU,OAAO;AAAA,MACpD;AACA,UAAI,QAAQ,IAAI,6BAA6B;AAC3C,eAAO,KAAK,KAAK,QAAQ,IAAI,6BAA6B,kBAAkB,UAAU;AAAA,MACxF;AACA,UAAI,gBAAgB;AAClB,eAAO,KAAK,KAAK,WAAW,QAAQ,cAAc;AAAA,MACpD,OAAO;AACL,cAAM,IAAI,MAAM,oGAAoG;AAAA,MACtH;AAAA,IACF;AAEA,WAAO,UAAU,gBAAgB;AAAA;AAAA;", "names": []}