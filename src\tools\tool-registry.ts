import { readdirSync, statSync } from 'fs';
import { join, extname } from 'path';
import type { Tool, ToolSchema, ToolResult } from '../types';

export class ToolRegistry {
  private static instance: ToolRegistry;
  private tools: Map<string, Tool> = new Map();
  private toolsLoaded = false;

  private constructor() {}

  public static getInstance(): ToolRegistry {
    if (!ToolRegistry.instance) {
      ToolRegistry.instance = new ToolRegistry();
    }
    return ToolRegistry.instance;
  }

  public async loadTools(): Promise<void> {
    if (this.toolsLoaded) {
      return;
    }

    try {
      const toolsDir = join(__dirname);
      const files = readdirSync(toolsDir);

      for (const file of files) {
        if (file === 'tool-registry.ts' || file === 'index.ts') {
          continue;
        }

        const filePath = join(toolsDir, file);
        const stat = statSync(filePath);

        if (stat.isFile() && (extname(file) === '.ts' || extname(file) === '.js')) {
          try {
            const toolModule = await import(filePath);
            
            if (toolModule.default && typeof toolModule.default === 'object') {
              const tool = toolModule.default as Tool;
              
              if (this.validateTool(tool)) {
                this.tools.set(tool.schema.name, tool);
                console.log(`Loaded tool: ${tool.schema.name}`);
              } else {
                console.warn(`Invalid tool in file: ${file}`);
              }
            }
          } catch (error) {
            console.error(`Failed to load tool from ${file}:`, error);
          }
        }
      }

      this.toolsLoaded = true;
      console.log(`Loaded ${this.tools.size} tools`);
    } catch (error) {
      console.error('Failed to load tools:', error);
    }
  }

  public getTool(name: string): Tool | undefined {
    return this.tools.get(name);
  }

  public getAllTools(): Tool[] {
    return Array.from(this.tools.values());
  }

  public getToolSchemas(): Record<string, any> {
    const schemas: Record<string, any> = {};
    for (const [name, tool] of this.tools) {
      schemas[name] = tool.schema;
    }
    return schemas;
  }

  public getToolNames(): string[] {
    return Array.from(this.tools.keys());
  }

  public async executeTool(name: string, args: Record<string, any>): Promise<ToolResult> {
    const tool = this.tools.get(name);
    
    if (!tool) {
      return {
        id: `${name}-${Date.now()}`,
        success: false,
        output: '',
        error: `Tool '${name}' not found`,
        executionTime: 0
      };
    }

    const startTime = Date.now();
    
    try {
      // Validate arguments against schema
      const validationResult = this.validateArguments(tool.schema, args);
      if (!validationResult.valid) {
        return {
          id: `${name}-${Date.now()}`,
          success: false,
          output: '',
          error: `Invalid arguments: ${validationResult.errors.join(', ')}`,
          executionTime: Date.now() - startTime
        };
      }

      const result = await tool.execute(args);
      result.executionTime = Date.now() - startTime;
      
      return result;
    } catch (error: any) {
      return {
        id: `${name}-${Date.now()}`,
        success: false,
        output: '',
        error: error.message || 'Unknown error occurred',
        executionTime: Date.now() - startTime
      };
    }
  }

  public registerTool(tool: Tool): void {
    if (!this.validateTool(tool)) {
      throw new Error(`Invalid tool: ${tool.schema?.name || 'unknown'}`);
    }

    this.tools.set(tool.schema.name, tool);
    console.log(`Registered tool: ${tool.schema.name}`);
  }

  public unregisterTool(name: string): boolean {
    return this.tools.delete(name);
  }

  public hasTools(): boolean {
    return this.tools.size > 0;
  }

  public getToolCount(): number {
    return this.tools.size;
  }

  public getToolsByCategory(category?: string): Tool[] {
    if (!category) {
      return this.getAllTools();
    }

    return Array.from(this.tools.values()).filter(tool => 
      tool.schema.description.toLowerCase().includes(category.toLowerCase())
    );
  }

  private validateTool(tool: any): tool is Tool {
    if (!tool || typeof tool !== 'object') {
      return false;
    }

    // Check if it has required properties
    if (!tool.schema || !tool.execute) {
      return false;
    }

    // Validate schema structure
    const schema = tool.schema;
    if (!schema.name || !schema.description || !schema.parameters) {
      return false;
    }

    // Check if execute is a function
    if (typeof tool.execute !== 'function') {
      return false;
    }

    // Validate parameters structure
    const params = schema.parameters;
    if (params.type !== 'object' || !params.properties || !Array.isArray(params.required)) {
      return false;
    }

    return true;
  }

  private validateArguments(schema: ToolSchema, args: Record<string, any>): {
    valid: boolean;
    errors: string[];
  } {
    const errors: string[] = [];
    const { parameters } = schema;

    // Check required parameters
    for (const requiredParam of parameters.required) {
      if (!(requiredParam in args)) {
        errors.push(`Missing required parameter: ${requiredParam}`);
      }
    }

    // Check parameter types and constraints
    for (const [paramName, paramValue] of Object.entries(args)) {
      const paramSchema = parameters.properties[paramName];
      
      if (!paramSchema) {
        errors.push(`Unknown parameter: ${paramName}`);
        continue;
      }

      // Type checking
      const expectedType = paramSchema.type;
      const actualType = typeof paramValue;

      if (expectedType === 'string' && actualType !== 'string') {
        errors.push(`Parameter '${paramName}' must be a string`);
      } else if (expectedType === 'number' && actualType !== 'number') {
        errors.push(`Parameter '${paramName}' must be a number`);
      } else if (expectedType === 'boolean' && actualType !== 'boolean') {
        errors.push(`Parameter '${paramName}' must be a boolean`);
      } else if (expectedType === 'array' && !Array.isArray(paramValue)) {
        errors.push(`Parameter '${paramName}' must be an array`);
      } else if (expectedType === 'object' && (actualType !== 'object' || Array.isArray(paramValue))) {
        errors.push(`Parameter '${paramName}' must be an object`);
      }

      // Enum validation
      if (paramSchema.enum && !paramSchema.enum.includes(paramValue)) {
        errors.push(`Parameter '${paramName}' must be one of: ${paramSchema.enum.join(', ')}`);
      }
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }

  public async reloadTools(): Promise<void> {
    this.tools.clear();
    this.toolsLoaded = false;
    await this.loadTools();
  }

  public getToolInfo(name: string): {
    name: string;
    description: string;
    parameters: any;
    examples?: string[];
  } | null {
    const tool = this.tools.get(name);
    if (!tool) {
      return null;
    }

    return {
      name: tool.schema.name,
      description: tool.schema.description,
      parameters: tool.schema.parameters,
      examples: (tool as any).examples || []
    };
  }

  public searchTools(query: string): Tool[] {
    const lowerQuery = query.toLowerCase();

    return Array.from(this.tools.values()).filter(tool =>
      tool.schema.name.toLowerCase().includes(lowerQuery) ||
      tool.schema.description.toLowerCase().includes(lowerQuery)
    );
  }

  public clearTools(): void {
    this.tools.clear();
  }
}
