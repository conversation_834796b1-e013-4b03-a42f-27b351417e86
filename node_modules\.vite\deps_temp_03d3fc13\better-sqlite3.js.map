{"version": 3, "sources": ["../../better-sqlite3/lib/util.js", "../../better-sqlite3/lib/sqlite-error.js", "../../file-uri-to-path/index.js", "../../bindings/bindings.js", "../../better-sqlite3/lib/methods/wrappers.js", "../../better-sqlite3/lib/methods/transaction.js", "../../better-sqlite3/lib/methods/pragma.js", "browser-external:util", "../../better-sqlite3/lib/methods/backup.js", "../../better-sqlite3/lib/methods/serialize.js", "../../better-sqlite3/lib/methods/function.js", "../../better-sqlite3/lib/methods/aggregate.js", "../../better-sqlite3/lib/methods/table.js", "../../better-sqlite3/lib/methods/inspect.js", "../../better-sqlite3/lib/database.js", "../../better-sqlite3/lib/index.js"], "sourcesContent": ["'use strict';\n\nexports.getBooleanOption = (options, key) => {\n\tlet value = false;\n\tif (key in options && typeof (value = options[key]) !== 'boolean') {\n\t\tthrow new TypeError(`Expected the \"${key}\" option to be a boolean`);\n\t}\n\treturn value;\n};\n\nexports.cppdb = Symbol();\nexports.inspect = Symbol.for('nodejs.util.inspect.custom');\n", "'use strict';\nconst descriptor = { value: 'SqliteError', writable: true, enumerable: false, configurable: true };\n\nfunction SqliteError(message, code) {\n\tif (new.target !== SqliteError) {\n\t\treturn new SqliteError(message, code);\n\t}\n\tif (typeof code !== 'string') {\n\t\tthrow new TypeError('Expected second argument to be a string');\n\t}\n\tError.call(this, message);\n\tdescriptor.value = '' + message;\n\tObject.defineProperty(this, 'message', descriptor);\n\tError.captureStackTrace(this, SqliteError);\n\tthis.code = code;\n}\nObject.setPrototypeOf(SqliteError, Error);\nObject.setPrototypeOf(SqliteError.prototype, Error.prototype);\nObject.defineProperty(SqliteError.prototype, 'name', descriptor);\nmodule.exports = SqliteError;\n", "\n/**\n * Module dependencies.\n */\n\nvar sep = require('path').sep || '/';\n\n/**\n * Module exports.\n */\n\nmodule.exports = fileUriToPath;\n\n/**\n * File URI to Path function.\n *\n * @param {String} uri\n * @return {String} path\n * @api public\n */\n\nfunction fileUriToPath (uri) {\n  if ('string' != typeof uri ||\n      uri.length <= 7 ||\n      'file://' != uri.substring(0, 7)) {\n    throw new TypeError('must pass in a file:// URI to convert to a file path');\n  }\n\n  var rest = decodeURI(uri.substring(7));\n  var firstSlash = rest.indexOf('/');\n  var host = rest.substring(0, firstSlash);\n  var path = rest.substring(firstSlash + 1);\n\n  // 2.  Scheme Definition\n  // As a special case, <host> can be the string \"localhost\" or the empty\n  // string; this is interpreted as \"the machine from which the URL is\n  // being interpreted\".\n  if ('localhost' == host) host = '';\n\n  if (host) {\n    host = sep + sep + host;\n  }\n\n  // 3.2  Drives, drive letters, mount points, file system root\n  // Drive letters are mapped into the top of a file URI in various ways,\n  // depending on the implementation; some applications substitute\n  // vertical bar (\"|\") for the colon after the drive letter, yielding\n  // \"file:///c|/tmp/test.txt\".  In some cases, the colon is left\n  // unchanged, as in \"file:///c:/tmp/test.txt\".  In other cases, the\n  // colon is simply omitted, as in \"file:///c/tmp/test.txt\".\n  path = path.replace(/^(.+)\\|/, '$1:');\n\n  // for Windows, we need to invert the path separators from what a URI uses\n  if (sep == '\\\\') {\n    path = path.replace(/\\//g, '\\\\');\n  }\n\n  if (/^.+\\:/.test(path)) {\n    // has Windows drive at beginning of path\n  } else {\n    // unix path…\n    path = sep + path;\n  }\n\n  return host + path;\n}\n", "/**\n * Module dependencies.\n */\n\nvar fs = require('fs'),\n  path = require('path'),\n  fileURLToPath = require('file-uri-to-path'),\n  join = path.join,\n  dirname = path.dirname,\n  exists =\n    (fs.accessSync &&\n      function(path) {\n        try {\n          fs.accessSync(path);\n        } catch (e) {\n          return false;\n        }\n        return true;\n      }) ||\n    fs.existsSync ||\n    path.existsSync,\n  defaults = {\n    arrow: process.env.NODE_BINDINGS_ARROW || ' → ',\n    compiled: process.env.NODE_BINDINGS_COMPILED_DIR || 'compiled',\n    platform: process.platform,\n    arch: process.arch,\n    nodePreGyp:\n      'node-v' +\n      process.versions.modules +\n      '-' +\n      process.platform +\n      '-' +\n      process.arch,\n    version: process.versions.node,\n    bindings: 'bindings.node',\n    try: [\n      // node-gyp's linked version in the \"build\" dir\n      ['module_root', 'build', 'bindings'],\n      // node-waf and gyp_addon (a.k.a node-gyp)\n      ['module_root', 'build', 'Debug', 'bindings'],\n      ['module_root', 'build', 'Release', 'bindings'],\n      // Debug files, for development (legacy behavior, remove for node v0.9)\n      ['module_root', 'out', 'Debug', 'bindings'],\n      ['module_root', 'Debug', 'bindings'],\n      // Release files, but manually compiled (legacy behavior, remove for node v0.9)\n      ['module_root', 'out', 'Release', 'bindings'],\n      ['module_root', 'Release', 'bindings'],\n      // Legacy from node-waf, node <= 0.4.x\n      ['module_root', 'build', 'default', 'bindings'],\n      // Production \"Release\" buildtype binary (meh...)\n      ['module_root', 'compiled', 'version', 'platform', 'arch', 'bindings'],\n      // node-qbs builds\n      ['module_root', 'addon-build', 'release', 'install-root', 'bindings'],\n      ['module_root', 'addon-build', 'debug', 'install-root', 'bindings'],\n      ['module_root', 'addon-build', 'default', 'install-root', 'bindings'],\n      // node-pre-gyp path ./lib/binding/{node_abi}-{platform}-{arch}\n      ['module_root', 'lib', 'binding', 'nodePreGyp', 'bindings']\n    ]\n  };\n\n/**\n * The main `bindings()` function loads the compiled bindings for a given module.\n * It uses V8's Error API to determine the parent filename that this function is\n * being invoked from, which is then used to find the root directory.\n */\n\nfunction bindings(opts) {\n  // Argument surgery\n  if (typeof opts == 'string') {\n    opts = { bindings: opts };\n  } else if (!opts) {\n    opts = {};\n  }\n\n  // maps `defaults` onto `opts` object\n  Object.keys(defaults).map(function(i) {\n    if (!(i in opts)) opts[i] = defaults[i];\n  });\n\n  // Get the module root\n  if (!opts.module_root) {\n    opts.module_root = exports.getRoot(exports.getFileName());\n  }\n\n  // Ensure the given bindings name ends with .node\n  if (path.extname(opts.bindings) != '.node') {\n    opts.bindings += '.node';\n  }\n\n  // https://github.com/webpack/webpack/issues/4175#issuecomment-342931035\n  var requireFunc =\n    typeof __webpack_require__ === 'function'\n      ? __non_webpack_require__\n      : require;\n\n  var tries = [],\n    i = 0,\n    l = opts.try.length,\n    n,\n    b,\n    err;\n\n  for (; i < l; i++) {\n    n = join.apply(\n      null,\n      opts.try[i].map(function(p) {\n        return opts[p] || p;\n      })\n    );\n    tries.push(n);\n    try {\n      b = opts.path ? requireFunc.resolve(n) : requireFunc(n);\n      if (!opts.path) {\n        b.path = n;\n      }\n      return b;\n    } catch (e) {\n      if (e.code !== 'MODULE_NOT_FOUND' &&\n          e.code !== 'QUALIFIED_PATH_RESOLUTION_FAILED' &&\n          !/not find/i.test(e.message)) {\n        throw e;\n      }\n    }\n  }\n\n  err = new Error(\n    'Could not locate the bindings file. Tried:\\n' +\n      tries\n        .map(function(a) {\n          return opts.arrow + a;\n        })\n        .join('\\n')\n  );\n  err.tries = tries;\n  throw err;\n}\nmodule.exports = exports = bindings;\n\n/**\n * Gets the filename of the JavaScript file that invokes this function.\n * Used to help find the root directory of a module.\n * Optionally accepts an filename argument to skip when searching for the invoking filename\n */\n\nexports.getFileName = function getFileName(calling_file) {\n  var origPST = Error.prepareStackTrace,\n    origSTL = Error.stackTraceLimit,\n    dummy = {},\n    fileName;\n\n  Error.stackTraceLimit = 10;\n\n  Error.prepareStackTrace = function(e, st) {\n    for (var i = 0, l = st.length; i < l; i++) {\n      fileName = st[i].getFileName();\n      if (fileName !== __filename) {\n        if (calling_file) {\n          if (fileName !== calling_file) {\n            return;\n          }\n        } else {\n          return;\n        }\n      }\n    }\n  };\n\n  // run the 'prepareStackTrace' function above\n  Error.captureStackTrace(dummy);\n  dummy.stack;\n\n  // cleanup\n  Error.prepareStackTrace = origPST;\n  Error.stackTraceLimit = origSTL;\n\n  // handle filename that starts with \"file://\"\n  var fileSchema = 'file://';\n  if (fileName.indexOf(fileSchema) === 0) {\n    fileName = fileURLToPath(fileName);\n  }\n\n  return fileName;\n};\n\n/**\n * Gets the root directory of a module, given an arbitrary filename\n * somewhere in the module tree. The \"root directory\" is the directory\n * containing the `package.json` file.\n *\n *   In:  /home/<USER>/node-native-module/lib/index.js\n *   Out: /home/<USER>/node-native-module\n */\n\nexports.getRoot = function getRoot(file) {\n  var dir = dirname(file),\n    prev;\n  while (true) {\n    if (dir === '.') {\n      // Avoids an infinite loop in rare cases, like the REPL\n      dir = process.cwd();\n    }\n    if (\n      exists(join(dir, 'package.json')) ||\n      exists(join(dir, 'node_modules'))\n    ) {\n      // Found the 'package.json' file or 'node_modules' dir; we're done\n      return dir;\n    }\n    if (prev === dir) {\n      // Got to the top\n      throw new Error(\n        'Could not find module root given file: \"' +\n          file +\n          '\". Do you have a `package.json` file? '\n      );\n    }\n    // Try the parent dir next\n    prev = dir;\n    dir = join(dir, '..');\n  }\n};\n", "'use strict';\nconst { cppdb } = require('../util');\n\nexports.prepare = function prepare(sql) {\n\treturn this[cppdb].prepare(sql, this, false);\n};\n\nexports.exec = function exec(sql) {\n\tthis[cppdb].exec(sql);\n\treturn this;\n};\n\nexports.close = function close() {\n\tthis[cppdb].close();\n\treturn this;\n};\n\nexports.loadExtension = function loadExtension(...args) {\n\tthis[cppdb].loadExtension(...args);\n\treturn this;\n};\n\nexports.defaultSafeIntegers = function defaultSafeIntegers(...args) {\n\tthis[cppdb].defaultSafeIntegers(...args);\n\treturn this;\n};\n\nexports.unsafeMode = function unsafeMode(...args) {\n\tthis[cppdb].unsafeMode(...args);\n\treturn this;\n};\n\nexports.getters = {\n\tname: {\n\t\tget: function name() { return this[cppdb].name; },\n\t\tenumerable: true,\n\t},\n\topen: {\n\t\tget: function open() { return this[cppdb].open; },\n\t\tenumerable: true,\n\t},\n\tinTransaction: {\n\t\tget: function inTransaction() { return this[cppdb].inTransaction; },\n\t\tenumerable: true,\n\t},\n\treadonly: {\n\t\tget: function readonly() { return this[cppdb].readonly; },\n\t\tenumerable: true,\n\t},\n\tmemory: {\n\t\tget: function memory() { return this[cppdb].memory; },\n\t\tenumerable: true,\n\t},\n};\n", "'use strict';\nconst { cppdb } = require('../util');\nconst controllers = new WeakMap();\n\nmodule.exports = function transaction(fn) {\n\tif (typeof fn !== 'function') throw new TypeError('Expected first argument to be a function');\n\n\tconst db = this[cppdb];\n\tconst controller = getController(db, this);\n\tconst { apply } = Function.prototype;\n\n\t// Each version of the transaction function has these same properties\n\tconst properties = {\n\t\tdefault: { value: wrapTransaction(apply, fn, db, controller.default) },\n\t\tdeferred: { value: wrapTransaction(apply, fn, db, controller.deferred) },\n\t\timmediate: { value: wrapTransaction(apply, fn, db, controller.immediate) },\n\t\texclusive: { value: wrapTransaction(apply, fn, db, controller.exclusive) },\n\t\tdatabase: { value: this, enumerable: true },\n\t};\n\n\tObject.defineProperties(properties.default.value, properties);\n\tObject.defineProperties(properties.deferred.value, properties);\n\tObject.defineProperties(properties.immediate.value, properties);\n\tObject.defineProperties(properties.exclusive.value, properties);\n\n\t// Return the default version of the transaction function\n\treturn properties.default.value;\n};\n\n// Return the database's cached transaction controller, or create a new one\nconst getController = (db, self) => {\n\tlet controller = controllers.get(db);\n\tif (!controller) {\n\t\tconst shared = {\n\t\t\tcommit: db.prepare('COMMIT', self, false),\n\t\t\trollback: db.prepare('ROLLBACK', self, false),\n\t\t\tsavepoint: db.prepare('SAVEPOINT `\\t_bs3.\\t`', self, false),\n\t\t\trelease: db.prepare('RELEASE `\\t_bs3.\\t`', self, false),\n\t\t\trollbackTo: db.prepare('ROLLBACK TO `\\t_bs3.\\t`', self, false),\n\t\t};\n\t\tcontrollers.set(db, controller = {\n\t\t\tdefault: Object.assign({ begin: db.prepare('BEGIN', self, false) }, shared),\n\t\t\tdeferred: Object.assign({ begin: db.prepare('BEGIN DEFERRED', self, false) }, shared),\n\t\t\timmediate: Object.assign({ begin: db.prepare('BEGIN IMMEDIATE', self, false) }, shared),\n\t\t\texclusive: Object.assign({ begin: db.prepare('BEGIN EXCLUSIVE', self, false) }, shared),\n\t\t});\n\t}\n\treturn controller;\n};\n\n// Return a new transaction function by wrapping the given function\nconst wrapTransaction = (apply, fn, db, { begin, commit, rollback, savepoint, release, rollbackTo }) => function sqliteTransaction() {\n\tlet before, after, undo;\n\tif (db.inTransaction) {\n\t\tbefore = savepoint;\n\t\tafter = release;\n\t\tundo = rollbackTo;\n\t} else {\n\t\tbefore = begin;\n\t\tafter = commit;\n\t\tundo = rollback;\n\t}\n\tbefore.run();\n\ttry {\n\t\tconst result = apply.call(fn, this, arguments);\n\t\tif (result && typeof result.then === 'function') {\n\t\t\tthrow new TypeError('Transaction function cannot return a promise');\n\t\t}\n\t\tafter.run();\n\t\treturn result;\n\t} catch (ex) {\n\t\tif (db.inTransaction) {\n\t\t\tundo.run();\n\t\t\tif (undo !== rollback) after.run();\n\t\t}\n\t\tthrow ex;\n\t}\n};\n", "'use strict';\nconst { getBooleanOption, cppdb } = require('../util');\n\nmodule.exports = function pragma(source, options) {\n\tif (options == null) options = {};\n\tif (typeof source !== 'string') throw new TypeError('Expected first argument to be a string');\n\tif (typeof options !== 'object') throw new TypeError('Expected second argument to be an options object');\n\tconst simple = getBooleanOption(options, 'simple');\n\n\tconst stmt = this[cppdb].prepare(`PRAGMA ${source}`, this, true);\n\treturn simple ? stmt.pluck().get() : stmt.all();\n};\n", "module.exports = Object.create(new Proxy({}, {\n  get(_, key) {\n    if (\n      key !== '__esModule' &&\n      key !== '__proto__' &&\n      key !== 'constructor' &&\n      key !== 'splice'\n    ) {\n      console.warn(`Module \"util\" has been externalized for browser compatibility. Cannot access \"util.${key}\" in client code. See https://vite.dev/guide/troubleshooting.html#module-externalized-for-browser-compatibility for more details.`)\n    }\n  }\n}))", "'use strict';\nconst fs = require('fs');\nconst path = require('path');\nconst { promisify } = require('util');\nconst { cppdb } = require('../util');\nconst fsAccess = promisify(fs.access);\n\nmodule.exports = async function backup(filename, options) {\n\tif (options == null) options = {};\n\n\t// Validate arguments\n\tif (typeof filename !== 'string') throw new TypeError('Expected first argument to be a string');\n\tif (typeof options !== 'object') throw new TypeError('Expected second argument to be an options object');\n\n\t// Interpret options\n\tfilename = filename.trim();\n\tconst attachedName = 'attached' in options ? options.attached : 'main';\n\tconst handler = 'progress' in options ? options.progress : null;\n\n\t// Validate interpreted options\n\tif (!filename) throw new TypeError('Backup filename cannot be an empty string');\n\tif (filename === ':memory:') throw new TypeError('Invalid backup filename \":memory:\"');\n\tif (typeof attachedName !== 'string') throw new TypeError('Expected the \"attached\" option to be a string');\n\tif (!attachedName) throw new TypeError('The \"attached\" option cannot be an empty string');\n\tif (handler != null && typeof handler !== 'function') throw new TypeError('Expected the \"progress\" option to be a function');\n\n\t// Make sure the specified directory exists\n\tawait fsAccess(path.dirname(filename)).catch(() => {\n\t\tthrow new TypeError('Cannot save backup because the directory does not exist');\n\t});\n\n\tconst isNewFile = await fsAccess(filename).then(() => false, () => true);\n\treturn runBackup(this[cppdb].backup(this, attachedName, filename, isNewFile), handler || null);\n};\n\nconst runBackup = (backup, handler) => {\n\tlet rate = 0;\n\tlet useDefault = true;\n\n\treturn new Promise((resolve, reject) => {\n\t\tsetImmediate(function step() {\n\t\t\ttry {\n\t\t\t\tconst progress = backup.transfer(rate);\n\t\t\t\tif (!progress.remainingPages) {\n\t\t\t\t\tbackup.close();\n\t\t\t\t\tresolve(progress);\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\tif (useDefault) {\n\t\t\t\t\tuseDefault = false;\n\t\t\t\t\trate = 100;\n\t\t\t\t}\n\t\t\t\tif (handler) {\n\t\t\t\t\tconst ret = handler(progress);\n\t\t\t\t\tif (ret !== undefined) {\n\t\t\t\t\t\tif (typeof ret === 'number' && ret === ret) rate = Math.max(0, Math.min(0x7fffffff, Math.round(ret)));\n\t\t\t\t\t\telse throw new TypeError('Expected progress callback to return a number or undefined');\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tsetImmediate(step);\n\t\t\t} catch (err) {\n\t\t\t\tbackup.close();\n\t\t\t\treject(err);\n\t\t\t}\n\t\t});\n\t});\n};\n", "'use strict';\nconst { cppdb } = require('../util');\n\nmodule.exports = function serialize(options) {\n\tif (options == null) options = {};\n\n\t// Validate arguments\n\tif (typeof options !== 'object') throw new TypeError('Expected first argument to be an options object');\n\n\t// Interpret and validate options\n\tconst attachedName = 'attached' in options ? options.attached : 'main';\n\tif (typeof attachedName !== 'string') throw new TypeError('Expected the \"attached\" option to be a string');\n\tif (!attachedName) throw new TypeError('The \"attached\" option cannot be an empty string');\n\n\treturn this[cppdb].serialize(attachedName);\n};\n", "'use strict';\nconst { getBooleanOption, cppdb } = require('../util');\n\nmodule.exports = function defineFunction(name, options, fn) {\n\t// Apply defaults\n\tif (options == null) options = {};\n\tif (typeof options === 'function') { fn = options; options = {}; }\n\n\t// Validate arguments\n\tif (typeof name !== 'string') throw new TypeError('Expected first argument to be a string');\n\tif (typeof fn !== 'function') throw new TypeError('Expected last argument to be a function');\n\tif (typeof options !== 'object') throw new TypeError('Expected second argument to be an options object');\n\tif (!name) throw new TypeError('User-defined function name cannot be an empty string');\n\n\t// Interpret options\n\tconst safeIntegers = 'safeIntegers' in options ? +getBooleanOption(options, 'safeIntegers') : 2;\n\tconst deterministic = getBooleanOption(options, 'deterministic');\n\tconst directOnly = getBooleanOption(options, 'directOnly');\n\tconst varargs = getBooleanOption(options, 'varargs');\n\tlet argCount = -1;\n\n\t// Determine argument count\n\tif (!varargs) {\n\t\targCount = fn.length;\n\t\tif (!Number.isInteger(argCount) || argCount < 0) throw new TypeError('Expected function.length to be a positive integer');\n\t\tif (argCount > 100) throw new RangeError('User-defined functions cannot have more than 100 arguments');\n\t}\n\n\tthis[cppdb].function(fn, name, argCount, safeIntegers, deterministic, directOnly);\n\treturn this;\n};\n", "'use strict';\nconst { getBooleanOption, cppdb } = require('../util');\n\nmodule.exports = function defineAggregate(name, options) {\n\t// Validate arguments\n\tif (typeof name !== 'string') throw new TypeError('Expected first argument to be a string');\n\tif (typeof options !== 'object' || options === null) throw new TypeError('Expected second argument to be an options object');\n\tif (!name) throw new TypeError('User-defined function name cannot be an empty string');\n\n\t// Interpret options\n\tconst start = 'start' in options ? options.start : null;\n\tconst step = getFunctionOption(options, 'step', true);\n\tconst inverse = getFunctionOption(options, 'inverse', false);\n\tconst result = getFunctionOption(options, 'result', false);\n\tconst safeIntegers = 'safeIntegers' in options ? +getBooleanOption(options, 'safeIntegers') : 2;\n\tconst deterministic = getBooleanOption(options, 'deterministic');\n\tconst directOnly = getBooleanOption(options, 'directOnly');\n\tconst varargs = getBooleanOption(options, 'varargs');\n\tlet argCount = -1;\n\n\t// Determine argument count\n\tif (!varargs) {\n\t\targCount = Math.max(getLength(step), inverse ? getLength(inverse) : 0);\n\t\tif (argCount > 0) argCount -= 1;\n\t\tif (argCount > 100) throw new RangeError('User-defined functions cannot have more than 100 arguments');\n\t}\n\n\tthis[cppdb].aggregate(start, step, inverse, result, name, argCount, safeIntegers, deterministic, directOnly);\n\treturn this;\n};\n\nconst getFunctionOption = (options, key, required) => {\n\tconst value = key in options ? options[key] : null;\n\tif (typeof value === 'function') return value;\n\tif (value != null) throw new TypeError(`Expected the \"${key}\" option to be a function`);\n\tif (required) throw new TypeError(`Missing required option \"${key}\"`);\n\treturn null;\n};\n\nconst getLength = ({ length }) => {\n\tif (Number.isInteger(length) && length >= 0) return length;\n\tthrow new TypeError('Expected function.length to be a positive integer');\n};\n", "'use strict';\nconst { cppdb } = require('../util');\n\nmodule.exports = function defineTable(name, factory) {\n\t// Validate arguments\n\tif (typeof name !== 'string') throw new TypeError('Expected first argument to be a string');\n\tif (!name) throw new TypeError('Virtual table module name cannot be an empty string');\n\n\t// Determine whether the module is eponymous-only or not\n\tlet eponymous = false;\n\tif (typeof factory === 'object' && factory !== null) {\n\t\teponymous = true;\n\t\tfactory = defer(parseTableDefinition(factory, 'used', name));\n\t} else {\n\t\tif (typeof factory !== 'function') throw new TypeError('Expected second argument to be a function or a table definition object');\n\t\tfactory = wrapFactory(factory);\n\t}\n\n\tthis[cppdb].table(factory, name, eponymous);\n\treturn this;\n};\n\nfunction wrapFactory(factory) {\n\treturn function virtualTableFactory(moduleName, databaseName, tableName, ...args) {\n\t\tconst thisObject = {\n\t\t\tmodule: moduleName,\n\t\t\tdatabase: databaseName,\n\t\t\ttable: tableName,\n\t\t};\n\n\t\t// Generate a new table definition by invoking the factory\n\t\tconst def = apply.call(factory, thisObject, args);\n\t\tif (typeof def !== 'object' || def === null) {\n\t\t\tthrow new TypeError(`Virtual table module \"${moduleName}\" did not return a table definition object`);\n\t\t}\n\n\t\treturn parseTableDefinition(def, 'returned', moduleName);\n\t};\n}\n\nfunction parseTableDefinition(def, verb, moduleName) {\n\t// Validate required properties\n\tif (!hasOwnProperty.call(def, 'rows')) {\n\t\tthrow new TypeError(`Virtual table module \"${moduleName}\" ${verb} a table definition without a \"rows\" property`);\n\t}\n\tif (!hasOwnProperty.call(def, 'columns')) {\n\t\tthrow new TypeError(`Virtual table module \"${moduleName}\" ${verb} a table definition without a \"columns\" property`);\n\t}\n\n\t// Validate \"rows\" property\n\tconst rows = def.rows;\n\tif (typeof rows !== 'function' || Object.getPrototypeOf(rows) !== GeneratorFunctionPrototype) {\n\t\tthrow new TypeError(`Virtual table module \"${moduleName}\" ${verb} a table definition with an invalid \"rows\" property (should be a generator function)`);\n\t}\n\n\t// Validate \"columns\" property\n\tlet columns = def.columns;\n\tif (!Array.isArray(columns) || !(columns = [...columns]).every(x => typeof x === 'string')) {\n\t\tthrow new TypeError(`Virtual table module \"${moduleName}\" ${verb} a table definition with an invalid \"columns\" property (should be an array of strings)`);\n\t}\n\tif (columns.length !== new Set(columns).size) {\n\t\tthrow new TypeError(`Virtual table module \"${moduleName}\" ${verb} a table definition with duplicate column names`);\n\t}\n\tif (!columns.length) {\n\t\tthrow new RangeError(`Virtual table module \"${moduleName}\" ${verb} a table definition with zero columns`);\n\t}\n\n\t// Validate \"parameters\" property\n\tlet parameters;\n\tif (hasOwnProperty.call(def, 'parameters')) {\n\t\tparameters = def.parameters;\n\t\tif (!Array.isArray(parameters) || !(parameters = [...parameters]).every(x => typeof x === 'string')) {\n\t\t\tthrow new TypeError(`Virtual table module \"${moduleName}\" ${verb} a table definition with an invalid \"parameters\" property (should be an array of strings)`);\n\t\t}\n\t} else {\n\t\tparameters = inferParameters(rows);\n\t}\n\tif (parameters.length !== new Set(parameters).size) {\n\t\tthrow new TypeError(`Virtual table module \"${moduleName}\" ${verb} a table definition with duplicate parameter names`);\n\t}\n\tif (parameters.length > 32) {\n\t\tthrow new RangeError(`Virtual table module \"${moduleName}\" ${verb} a table definition with more than the maximum number of 32 parameters`);\n\t}\n\tfor (const parameter of parameters) {\n\t\tif (columns.includes(parameter)) {\n\t\t\tthrow new TypeError(`Virtual table module \"${moduleName}\" ${verb} a table definition with column \"${parameter}\" which was ambiguously defined as both a column and parameter`);\n\t\t}\n\t}\n\n\t// Validate \"safeIntegers\" option\n\tlet safeIntegers = 2;\n\tif (hasOwnProperty.call(def, 'safeIntegers')) {\n\t\tconst bool = def.safeIntegers;\n\t\tif (typeof bool !== 'boolean') {\n\t\t\tthrow new TypeError(`Virtual table module \"${moduleName}\" ${verb} a table definition with an invalid \"safeIntegers\" property (should be a boolean)`);\n\t\t}\n\t\tsafeIntegers = +bool;\n\t}\n\n\t// Validate \"directOnly\" option\n\tlet directOnly = false;\n\tif (hasOwnProperty.call(def, 'directOnly')) {\n\t\tdirectOnly = def.directOnly;\n\t\tif (typeof directOnly !== 'boolean') {\n\t\t\tthrow new TypeError(`Virtual table module \"${moduleName}\" ${verb} a table definition with an invalid \"directOnly\" property (should be a boolean)`);\n\t\t}\n\t}\n\n\t// Generate SQL for the virtual table definition\n\tconst columnDefinitions = [\n\t\t...parameters.map(identifier).map(str => `${str} HIDDEN`),\n\t\t...columns.map(identifier),\n\t];\n\treturn [\n\t\t`CREATE TABLE x(${columnDefinitions.join(', ')});`,\n\t\twrapGenerator(rows, new Map(columns.map((x, i) => [x, parameters.length + i])), moduleName),\n\t\tparameters,\n\t\tsafeIntegers,\n\t\tdirectOnly,\n\t];\n}\n\nfunction wrapGenerator(generator, columnMap, moduleName) {\n\treturn function* virtualTable(...args) {\n\t\t/*\n\t\t\tWe must defensively clone any buffers in the arguments, because\n\t\t\totherwise the generator could mutate one of them, which would cause\n\t\t\tus to return incorrect values for hidden columns, potentially\n\t\t\tcorrupting the database.\n\t\t */\n\t\tconst output = args.map(x => Buffer.isBuffer(x) ? Buffer.from(x) : x);\n\t\tfor (let i = 0; i < columnMap.size; ++i) {\n\t\t\toutput.push(null); // Fill with nulls to prevent gaps in array (v8 optimization)\n\t\t}\n\t\tfor (const row of generator(...args)) {\n\t\t\tif (Array.isArray(row)) {\n\t\t\t\textractRowArray(row, output, columnMap.size, moduleName);\n\t\t\t\tyield output;\n\t\t\t} else if (typeof row === 'object' && row !== null) {\n\t\t\t\textractRowObject(row, output, columnMap, moduleName);\n\t\t\t\tyield output;\n\t\t\t} else {\n\t\t\t\tthrow new TypeError(`Virtual table module \"${moduleName}\" yielded something that isn't a valid row object`);\n\t\t\t}\n\t\t}\n\t};\n}\n\nfunction extractRowArray(row, output, columnCount, moduleName) {\n\tif (row.length !== columnCount) {\n\t\tthrow new TypeError(`Virtual table module \"${moduleName}\" yielded a row with an incorrect number of columns`);\n\t}\n\tconst offset = output.length - columnCount;\n\tfor (let i = 0; i < columnCount; ++i) {\n\t\toutput[i + offset] = row[i];\n\t}\n}\n\nfunction extractRowObject(row, output, columnMap, moduleName) {\n\tlet count = 0;\n\tfor (const key of Object.keys(row)) {\n\t\tconst index = columnMap.get(key);\n\t\tif (index === undefined) {\n\t\t\tthrow new TypeError(`Virtual table module \"${moduleName}\" yielded a row with an undeclared column \"${key}\"`);\n\t\t}\n\t\toutput[index] = row[key];\n\t\tcount += 1;\n\t}\n\tif (count !== columnMap.size) {\n\t\tthrow new TypeError(`Virtual table module \"${moduleName}\" yielded a row with missing columns`);\n\t}\n}\n\nfunction inferParameters({ length }) {\n\tif (!Number.isInteger(length) || length < 0) {\n\t\tthrow new TypeError('Expected function.length to be a positive integer');\n\t}\n\tconst params = [];\n\tfor (let i = 0; i < length; ++i) {\n\t\tparams.push(`$${i + 1}`);\n\t}\n\treturn params;\n}\n\nconst { hasOwnProperty } = Object.prototype;\nconst { apply } = Function.prototype;\nconst GeneratorFunctionPrototype = Object.getPrototypeOf(function*(){});\nconst identifier = str => `\"${str.replace(/\"/g, '\"\"')}\"`;\nconst defer = x => () => x;\n", "'use strict';\nconst DatabaseInspection = function Database() {};\n\nmodule.exports = function inspect(depth, opts) {\n\treturn Object.assign(new DatabaseInspection(), this);\n};\n\n", "'use strict';\nconst fs = require('fs');\nconst path = require('path');\nconst util = require('./util');\nconst SqliteError = require('./sqlite-error');\n\nlet DEFAULT_ADDON;\n\nfunction Database(filenameGiven, options) {\n\tif (new.target == null) {\n\t\treturn new Database(filenameGiven, options);\n\t}\n\n\t// Apply defaults\n\tlet buffer;\n\tif (Buffer.isBuffer(filenameGiven)) {\n\t\tbuffer = filenameGiven;\n\t\tfilenameGiven = ':memory:';\n\t}\n\tif (filenameGiven == null) filenameGiven = '';\n\tif (options == null) options = {};\n\n\t// Validate arguments\n\tif (typeof filenameGiven !== 'string') throw new TypeError('Expected first argument to be a string');\n\tif (typeof options !== 'object') throw new TypeError('Expected second argument to be an options object');\n\tif ('readOnly' in options) throw new TypeError('Misspelled option \"readOnly\" should be \"readonly\"');\n\tif ('memory' in options) throw new TypeError('Option \"memory\" was removed in v7.0.0 (use \":memory:\" filename instead)');\n\n\t// Interpret options\n\tconst filename = filenameGiven.trim();\n\tconst anonymous = filename === '' || filename === ':memory:';\n\tconst readonly = util.getBooleanOption(options, 'readonly');\n\tconst fileMustExist = util.getBooleanOption(options, 'fileMustExist');\n\tconst timeout = 'timeout' in options ? options.timeout : 5000;\n\tconst verbose = 'verbose' in options ? options.verbose : null;\n\tconst nativeBinding = 'nativeBinding' in options ? options.nativeBinding : null;\n\n\t// Validate interpreted options\n\tif (readonly && anonymous && !buffer) throw new TypeError('In-memory/temporary databases cannot be readonly');\n\tif (!Number.isInteger(timeout) || timeout < 0) throw new TypeError('Expected the \"timeout\" option to be a positive integer');\n\tif (timeout > 0x7fffffff) throw new RangeError('Option \"timeout\" cannot be greater than 2147483647');\n\tif (verbose != null && typeof verbose !== 'function') throw new TypeError('Expected the \"verbose\" option to be a function');\n\tif (nativeBinding != null && typeof nativeBinding !== 'string' && typeof nativeBinding !== 'object') throw new TypeError('Expected the \"nativeBinding\" option to be a string or addon object');\n\n\t// Load the native addon\n\tlet addon;\n\tif (nativeBinding == null) {\n\t\taddon = DEFAULT_ADDON || (DEFAULT_ADDON = require('bindings')('better_sqlite3.node'));\n\t} else if (typeof nativeBinding === 'string') {\n\t\t// See <https://webpack.js.org/api/module-variables/#__non_webpack_require__-webpack-specific>\n\t\tconst requireFunc = typeof __non_webpack_require__ === 'function' ? __non_webpack_require__ : require;\n\t\taddon = requireFunc(path.resolve(nativeBinding).replace(/(\\.node)?$/, '.node'));\n\t} else {\n\t\t// See <https://github.com/WiseLibs/better-sqlite3/issues/972>\n\t\taddon = nativeBinding;\n\t}\n\n\tif (!addon.isInitialized) {\n\t\taddon.setErrorConstructor(SqliteError);\n\t\taddon.isInitialized = true;\n\t}\n\n\t// Make sure the specified directory exists\n\tif (!anonymous && !filename.startsWith('file:') && !fs.existsSync(path.dirname(filename))) {\n\t\tthrow new TypeError('Cannot open database because the directory does not exist');\n\t}\n\n\tObject.defineProperties(this, {\n\t\t[util.cppdb]: { value: new addon.Database(filename, filenameGiven, anonymous, readonly, fileMustExist, timeout, verbose || null, buffer || null) },\n\t\t...wrappers.getters,\n\t});\n}\n\nconst wrappers = require('./methods/wrappers');\nDatabase.prototype.prepare = wrappers.prepare;\nDatabase.prototype.transaction = require('./methods/transaction');\nDatabase.prototype.pragma = require('./methods/pragma');\nDatabase.prototype.backup = require('./methods/backup');\nDatabase.prototype.serialize = require('./methods/serialize');\nDatabase.prototype.function = require('./methods/function');\nDatabase.prototype.aggregate = require('./methods/aggregate');\nDatabase.prototype.table = require('./methods/table');\nDatabase.prototype.loadExtension = wrappers.loadExtension;\nDatabase.prototype.exec = wrappers.exec;\nDatabase.prototype.close = wrappers.close;\nDatabase.prototype.defaultSafeIntegers = wrappers.defaultSafeIntegers;\nDatabase.prototype.unsafeMode = wrappers.unsafeMode;\nDatabase.prototype[util.inspect] = require('./methods/inspect');\n\nmodule.exports = Database;\n", "'use strict';\nmodule.exports = require('./database');\nmodule.exports.SqliteError = require('./sqlite-error');\n"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAEA,YAAQ,mBAAmB,CAAC,SAAS,QAAQ;AAC5C,UAAI,QAAQ;AACZ,UAAI,OAAO,WAAW,QAAQ,QAAQ,QAAQ,GAAG,OAAO,WAAW;AAClE,cAAM,IAAI,UAAU,iBAAiB,GAAG,0BAA0B;AAAA,MACnE;AACA,aAAO;AAAA,IACR;AAEA,YAAQ,QAAQ,OAAO;AACvB,YAAQ,UAAU,OAAO,IAAI,4BAA4B;AAAA;AAAA;;;ACXzD;AAAA;AAAA;AACA,QAAM,aAAa,EAAE,OAAO,eAAe,UAAU,MAAM,YAAY,OAAO,cAAc,KAAK;AAEjG,aAAS,YAAY,SAAS,MAAM;AACnC,UAAI,eAAe,aAAa;AAC/B,eAAO,IAAI,YAAY,SAAS,IAAI;AAAA,MACrC;AACA,UAAI,OAAO,SAAS,UAAU;AAC7B,cAAM,IAAI,UAAU,yCAAyC;AAAA,MAC9D;AACA,YAAM,KAAK,MAAM,OAAO;AACxB,iBAAW,QAAQ,KAAK;AACxB,aAAO,eAAe,MAAM,WAAW,UAAU;AACjD,YAAM,kBAAkB,MAAM,WAAW;AACzC,WAAK,OAAO;AAAA,IACb;AACA,WAAO,eAAe,aAAa,KAAK;AACxC,WAAO,eAAe,YAAY,WAAW,MAAM,SAAS;AAC5D,WAAO,eAAe,YAAY,WAAW,QAAQ,UAAU;AAC/D,WAAO,UAAU;AAAA;AAAA;;;ACnBjB;AAAA;AAKA,QAAI,MAAM,eAAgB,OAAO;AAMjC,WAAO,UAAU;AAUjB,aAAS,cAAe,KAAK;AAC3B,UAAI,YAAY,OAAO,OACnB,IAAI,UAAU,KACd,aAAa,IAAI,UAAU,GAAG,CAAC,GAAG;AACpC,cAAM,IAAI,UAAU,sDAAsD;AAAA,MAC5E;AAEA,UAAI,OAAO,UAAU,IAAI,UAAU,CAAC,CAAC;AACrC,UAAI,aAAa,KAAK,QAAQ,GAAG;AACjC,UAAI,OAAO,KAAK,UAAU,GAAG,UAAU;AACvC,UAAI,OAAO,KAAK,UAAU,aAAa,CAAC;AAMxC,UAAI,eAAe,KAAM,QAAO;AAEhC,UAAI,MAAM;AACR,eAAO,MAAM,MAAM;AAAA,MACrB;AASA,aAAO,KAAK,QAAQ,WAAW,KAAK;AAGpC,UAAI,OAAO,MAAM;AACf,eAAO,KAAK,QAAQ,OAAO,IAAI;AAAA,MACjC;AAEA,UAAI,QAAQ,KAAK,IAAI,GAAG;AAAA,MAExB,OAAO;AAEL,eAAO,MAAM;AAAA,MACf;AAEA,aAAO,OAAO;AAAA,IAChB;AAAA;AAAA;;;ACjEA;AAAA;AAIA,QAAI,KAAK;AAAT,QACE,OAAO;AADT,QAEE,gBAAgB;AAFlB,QAGE,OAAO,KAAK;AAHd,QAIE,UAAU,KAAK;AAJjB,QAKE,SACG,GAAG,cACF,SAASA,OAAM;AACb,UAAI;AACF,WAAG,WAAWA,KAAI;AAAA,MACpB,SAAS,GAAG;AACV,eAAO;AAAA,MACT;AACA,aAAO;AAAA,IACT,KACF,GAAG,cACH,KAAK;AAhBT,QAiBE,WAAW;AAAA,MACT,OAAO,QAAQ,IAAI,uBAAuB;AAAA,MAC1C,UAAU,QAAQ,IAAI,8BAA8B;AAAA,MACpD,UAAU,QAAQ;AAAA,MAClB,MAAM,QAAQ;AAAA,MACd,YACE,WACA,QAAQ,SAAS,UACjB,MACA,QAAQ,WACR,MACA,QAAQ;AAAA,MACV,SAAS,QAAQ,SAAS;AAAA,MAC1B,UAAU;AAAA,MACV,KAAK;AAAA;AAAA,QAEH,CAAC,eAAe,SAAS,UAAU;AAAA;AAAA,QAEnC,CAAC,eAAe,SAAS,SAAS,UAAU;AAAA,QAC5C,CAAC,eAAe,SAAS,WAAW,UAAU;AAAA;AAAA,QAE9C,CAAC,eAAe,OAAO,SAAS,UAAU;AAAA,QAC1C,CAAC,eAAe,SAAS,UAAU;AAAA;AAAA,QAEnC,CAAC,eAAe,OAAO,WAAW,UAAU;AAAA,QAC5C,CAAC,eAAe,WAAW,UAAU;AAAA;AAAA,QAErC,CAAC,eAAe,SAAS,WAAW,UAAU;AAAA;AAAA,QAE9C,CAAC,eAAe,YAAY,WAAW,YAAY,QAAQ,UAAU;AAAA;AAAA,QAErE,CAAC,eAAe,eAAe,WAAW,gBAAgB,UAAU;AAAA,QACpE,CAAC,eAAe,eAAe,SAAS,gBAAgB,UAAU;AAAA,QAClE,CAAC,eAAe,eAAe,WAAW,gBAAgB,UAAU;AAAA;AAAA,QAEpE,CAAC,eAAe,OAAO,WAAW,cAAc,UAAU;AAAA,MAC5D;AAAA,IACF;AAQF,aAAS,SAAS,MAAM;AAEtB,UAAI,OAAO,QAAQ,UAAU;AAC3B,eAAO,EAAE,UAAU,KAAK;AAAA,MAC1B,WAAW,CAAC,MAAM;AAChB,eAAO,CAAC;AAAA,MACV;AAGA,aAAO,KAAK,QAAQ,EAAE,IAAI,SAASC,IAAG;AACpC,YAAI,EAAEA,MAAK,MAAO,MAAKA,EAAC,IAAI,SAASA,EAAC;AAAA,MACxC,CAAC;AAGD,UAAI,CAAC,KAAK,aAAa;AACrB,aAAK,cAAc,QAAQ,QAAQ,QAAQ,YAAY,CAAC;AAAA,MAC1D;AAGA,UAAI,KAAK,QAAQ,KAAK,QAAQ,KAAK,SAAS;AAC1C,aAAK,YAAY;AAAA,MACnB;AAGA,UAAI,cACF,OAAO,wBAAwB,aAC3B,0BACA;AAEN,UAAI,QAAQ,CAAC,GACX,IAAI,GACJ,IAAI,KAAK,IAAI,QACb,GACA,GACA;AAEF,aAAO,IAAI,GAAG,KAAK;AACjB,YAAI,KAAK;AAAA,UACP;AAAA,UACA,KAAK,IAAI,CAAC,EAAE,IAAI,SAAS,GAAG;AAC1B,mBAAO,KAAK,CAAC,KAAK;AAAA,UACpB,CAAC;AAAA,QACH;AACA,cAAM,KAAK,CAAC;AACZ,YAAI;AACF,cAAI,KAAK,OAAO,YAAY,QAAQ,CAAC,IAAI,YAAY,CAAC;AACtD,cAAI,CAAC,KAAK,MAAM;AACd,cAAE,OAAO;AAAA,UACX;AACA,iBAAO;AAAA,QACT,SAAS,GAAG;AACV,cAAI,EAAE,SAAS,sBACX,EAAE,SAAS,sCACX,CAAC,YAAY,KAAK,EAAE,OAAO,GAAG;AAChC,kBAAM;AAAA,UACR;AAAA,QACF;AAAA,MACF;AAEA,YAAM,IAAI;AAAA,QACR,iDACE,MACG,IAAI,SAAS,GAAG;AACf,iBAAO,KAAK,QAAQ;AAAA,QACtB,CAAC,EACA,KAAK,IAAI;AAAA,MAChB;AACA,UAAI,QAAQ;AACZ,YAAM;AAAA,IACR;AACA,WAAO,UAAU,UAAU;AAQ3B,YAAQ,cAAc,SAAS,YAAY,cAAc;AACvD,UAAI,UAAU,MAAM,mBAClB,UAAU,MAAM,iBAChB,QAAQ,CAAC,GACT;AAEF,YAAM,kBAAkB;AAExB,YAAM,oBAAoB,SAAS,GAAG,IAAI;AACxC,iBAAS,IAAI,GAAG,IAAI,GAAG,QAAQ,IAAI,GAAG,KAAK;AACzC,qBAAW,GAAG,CAAC,EAAE,YAAY;AAC7B,cAAI,aAAa,YAAY;AAC3B,gBAAI,cAAc;AAChB,kBAAI,aAAa,cAAc;AAC7B;AAAA,cACF;AAAA,YACF,OAAO;AACL;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAGA,YAAM,kBAAkB,KAAK;AAC7B,YAAM;AAGN,YAAM,oBAAoB;AAC1B,YAAM,kBAAkB;AAGxB,UAAI,aAAa;AACjB,UAAI,SAAS,QAAQ,UAAU,MAAM,GAAG;AACtC,mBAAW,cAAc,QAAQ;AAAA,MACnC;AAEA,aAAO;AAAA,IACT;AAWA,YAAQ,UAAU,SAAS,QAAQ,MAAM;AACvC,UAAI,MAAM,QAAQ,IAAI,GACpB;AACF,aAAO,MAAM;AACX,YAAI,QAAQ,KAAK;AAEf,gBAAM,QAAQ,IAAI;AAAA,QACpB;AACA,YACE,OAAO,KAAK,KAAK,cAAc,CAAC,KAChC,OAAO,KAAK,KAAK,cAAc,CAAC,GAChC;AAEA,iBAAO;AAAA,QACT;AACA,YAAI,SAAS,KAAK;AAEhB,gBAAM,IAAI;AAAA,YACR,6CACE,OACA;AAAA,UACJ;AAAA,QACF;AAEA,eAAO;AACP,cAAM,KAAK,KAAK,IAAI;AAAA,MACtB;AAAA,IACF;AAAA;AAAA;;;AC5NA;AAAA;AAAA;AACA,QAAM,EAAE,MAAM,IAAI;AAElB,YAAQ,UAAU,SAAS,QAAQ,KAAK;AACvC,aAAO,KAAK,KAAK,EAAE,QAAQ,KAAK,MAAM,KAAK;AAAA,IAC5C;AAEA,YAAQ,OAAO,SAAS,KAAK,KAAK;AACjC,WAAK,KAAK,EAAE,KAAK,GAAG;AACpB,aAAO;AAAA,IACR;AAEA,YAAQ,QAAQ,SAAS,QAAQ;AAChC,WAAK,KAAK,EAAE,MAAM;AAClB,aAAO;AAAA,IACR;AAEA,YAAQ,gBAAgB,SAAS,iBAAiB,MAAM;AACvD,WAAK,KAAK,EAAE,cAAc,GAAG,IAAI;AACjC,aAAO;AAAA,IACR;AAEA,YAAQ,sBAAsB,SAAS,uBAAuB,MAAM;AACnE,WAAK,KAAK,EAAE,oBAAoB,GAAG,IAAI;AACvC,aAAO;AAAA,IACR;AAEA,YAAQ,aAAa,SAAS,cAAc,MAAM;AACjD,WAAK,KAAK,EAAE,WAAW,GAAG,IAAI;AAC9B,aAAO;AAAA,IACR;AAEA,YAAQ,UAAU;AAAA,MACjB,MAAM;AAAA,QACL,KAAK,SAAS,OAAO;AAAE,iBAAO,KAAK,KAAK,EAAE;AAAA,QAAM;AAAA,QAChD,YAAY;AAAA,MACb;AAAA,MACA,MAAM;AAAA,QACL,KAAK,SAAS,OAAO;AAAE,iBAAO,KAAK,KAAK,EAAE;AAAA,QAAM;AAAA,QAChD,YAAY;AAAA,MACb;AAAA,MACA,eAAe;AAAA,QACd,KAAK,SAAS,gBAAgB;AAAE,iBAAO,KAAK,KAAK,EAAE;AAAA,QAAe;AAAA,QAClE,YAAY;AAAA,MACb;AAAA,MACA,UAAU;AAAA,QACT,KAAK,SAAS,WAAW;AAAE,iBAAO,KAAK,KAAK,EAAE;AAAA,QAAU;AAAA,QACxD,YAAY;AAAA,MACb;AAAA,MACA,QAAQ;AAAA,QACP,KAAK,SAAS,SAAS;AAAE,iBAAO,KAAK,KAAK,EAAE;AAAA,QAAQ;AAAA,QACpD,YAAY;AAAA,MACb;AAAA,IACD;AAAA;AAAA;;;ACrDA;AAAA;AAAA;AACA,QAAM,EAAE,MAAM,IAAI;AAClB,QAAM,cAAc,oBAAI,QAAQ;AAEhC,WAAO,UAAU,SAAS,YAAY,IAAI;AACzC,UAAI,OAAO,OAAO,WAAY,OAAM,IAAI,UAAU,0CAA0C;AAE5F,YAAM,KAAK,KAAK,KAAK;AACrB,YAAM,aAAa,cAAc,IAAI,IAAI;AACzC,YAAM,EAAE,MAAM,IAAI,SAAS;AAG3B,YAAM,aAAa;AAAA,QAClB,SAAS,EAAE,OAAO,gBAAgB,OAAO,IAAI,IAAI,WAAW,OAAO,EAAE;AAAA,QACrE,UAAU,EAAE,OAAO,gBAAgB,OAAO,IAAI,IAAI,WAAW,QAAQ,EAAE;AAAA,QACvE,WAAW,EAAE,OAAO,gBAAgB,OAAO,IAAI,IAAI,WAAW,SAAS,EAAE;AAAA,QACzE,WAAW,EAAE,OAAO,gBAAgB,OAAO,IAAI,IAAI,WAAW,SAAS,EAAE;AAAA,QACzE,UAAU,EAAE,OAAO,MAAM,YAAY,KAAK;AAAA,MAC3C;AAEA,aAAO,iBAAiB,WAAW,QAAQ,OAAO,UAAU;AAC5D,aAAO,iBAAiB,WAAW,SAAS,OAAO,UAAU;AAC7D,aAAO,iBAAiB,WAAW,UAAU,OAAO,UAAU;AAC9D,aAAO,iBAAiB,WAAW,UAAU,OAAO,UAAU;AAG9D,aAAO,WAAW,QAAQ;AAAA,IAC3B;AAGA,QAAM,gBAAgB,CAAC,IAAI,SAAS;AACnC,UAAI,aAAa,YAAY,IAAI,EAAE;AACnC,UAAI,CAAC,YAAY;AAChB,cAAM,SAAS;AAAA,UACd,QAAQ,GAAG,QAAQ,UAAU,MAAM,KAAK;AAAA,UACxC,UAAU,GAAG,QAAQ,YAAY,MAAM,KAAK;AAAA,UAC5C,WAAW,GAAG,QAAQ,uBAAyB,MAAM,KAAK;AAAA,UAC1D,SAAS,GAAG,QAAQ,qBAAuB,MAAM,KAAK;AAAA,UACtD,YAAY,GAAG,QAAQ,yBAA2B,MAAM,KAAK;AAAA,QAC9D;AACA,oBAAY,IAAI,IAAI,aAAa;AAAA,UAChC,SAAS,OAAO,OAAO,EAAE,OAAO,GAAG,QAAQ,SAAS,MAAM,KAAK,EAAE,GAAG,MAAM;AAAA,UAC1E,UAAU,OAAO,OAAO,EAAE,OAAO,GAAG,QAAQ,kBAAkB,MAAM,KAAK,EAAE,GAAG,MAAM;AAAA,UACpF,WAAW,OAAO,OAAO,EAAE,OAAO,GAAG,QAAQ,mBAAmB,MAAM,KAAK,EAAE,GAAG,MAAM;AAAA,UACtF,WAAW,OAAO,OAAO,EAAE,OAAO,GAAG,QAAQ,mBAAmB,MAAM,KAAK,EAAE,GAAG,MAAM;AAAA,QACvF,CAAC;AAAA,MACF;AACA,aAAO;AAAA,IACR;AAGA,QAAM,kBAAkB,CAAC,OAAO,IAAI,IAAI,EAAE,OAAO,QAAQ,UAAU,WAAW,SAAS,WAAW,MAAM,SAAS,oBAAoB;AACpI,UAAI,QAAQ,OAAO;AACnB,UAAI,GAAG,eAAe;AACrB,iBAAS;AACT,gBAAQ;AACR,eAAO;AAAA,MACR,OAAO;AACN,iBAAS;AACT,gBAAQ;AACR,eAAO;AAAA,MACR;AACA,aAAO,IAAI;AACX,UAAI;AACH,cAAM,SAAS,MAAM,KAAK,IAAI,MAAM,SAAS;AAC7C,YAAI,UAAU,OAAO,OAAO,SAAS,YAAY;AAChD,gBAAM,IAAI,UAAU,8CAA8C;AAAA,QACnE;AACA,cAAM,IAAI;AACV,eAAO;AAAA,MACR,SAAS,IAAI;AACZ,YAAI,GAAG,eAAe;AACrB,eAAK,IAAI;AACT,cAAI,SAAS,SAAU,OAAM,IAAI;AAAA,QAClC;AACA,cAAM;AAAA,MACP;AAAA,IACD;AAAA;AAAA;;;AC7EA;AAAA;AAAA;AACA,QAAM,EAAE,kBAAkB,MAAM,IAAI;AAEpC,WAAO,UAAU,SAAS,OAAO,QAAQ,SAAS;AACjD,UAAI,WAAW,KAAM,WAAU,CAAC;AAChC,UAAI,OAAO,WAAW,SAAU,OAAM,IAAI,UAAU,wCAAwC;AAC5F,UAAI,OAAO,YAAY,SAAU,OAAM,IAAI,UAAU,kDAAkD;AACvG,YAAM,SAAS,iBAAiB,SAAS,QAAQ;AAEjD,YAAM,OAAO,KAAK,KAAK,EAAE,QAAQ,UAAU,MAAM,IAAI,MAAM,IAAI;AAC/D,aAAO,SAAS,KAAK,MAAM,EAAE,IAAI,IAAI,KAAK,IAAI;AAAA,IAC/C;AAAA;AAAA;;;ACXA,IAAAC,gBAAA;AAAA;AAAA,WAAO,UAAU,OAAO,OAAO,IAAI,MAAM,CAAC,GAAG;AAAA,MAC3C,IAAI,GAAG,KAAK;AACV,YACE,QAAQ,gBACR,QAAQ,eACR,QAAQ,iBACR,QAAQ,UACR;AACA,kBAAQ,KAAK,sFAAsF,GAAG,mIAAmI;AAAA,QAC3O;AAAA,MACF;AAAA,IACF,CAAC,CAAC;AAAA;AAAA;;;ACXF;AAAA;AAAA;AACA,QAAM,KAAK;AACX,QAAM,OAAO;AACb,QAAM,EAAE,UAAU,IAAI;AACtB,QAAM,EAAE,MAAM,IAAI;AAClB,QAAM,WAAW,UAAU,GAAG,MAAM;AAEpC,WAAO,UAAU,eAAe,OAAO,UAAU,SAAS;AACzD,UAAI,WAAW,KAAM,WAAU,CAAC;AAGhC,UAAI,OAAO,aAAa,SAAU,OAAM,IAAI,UAAU,wCAAwC;AAC9F,UAAI,OAAO,YAAY,SAAU,OAAM,IAAI,UAAU,kDAAkD;AAGvG,iBAAW,SAAS,KAAK;AACzB,YAAM,eAAe,cAAc,UAAU,QAAQ,WAAW;AAChE,YAAM,UAAU,cAAc,UAAU,QAAQ,WAAW;AAG3D,UAAI,CAAC,SAAU,OAAM,IAAI,UAAU,2CAA2C;AAC9E,UAAI,aAAa,WAAY,OAAM,IAAI,UAAU,oCAAoC;AACrF,UAAI,OAAO,iBAAiB,SAAU,OAAM,IAAI,UAAU,+CAA+C;AACzG,UAAI,CAAC,aAAc,OAAM,IAAI,UAAU,iDAAiD;AACxF,UAAI,WAAW,QAAQ,OAAO,YAAY,WAAY,OAAM,IAAI,UAAU,iDAAiD;AAG3H,YAAM,SAAS,KAAK,QAAQ,QAAQ,CAAC,EAAE,MAAM,MAAM;AAClD,cAAM,IAAI,UAAU,yDAAyD;AAAA,MAC9E,CAAC;AAED,YAAM,YAAY,MAAM,SAAS,QAAQ,EAAE,KAAK,MAAM,OAAO,MAAM,IAAI;AACvE,aAAO,UAAU,KAAK,KAAK,EAAE,OAAO,MAAM,cAAc,UAAU,SAAS,GAAG,WAAW,IAAI;AAAA,IAC9F;AAEA,QAAM,YAAY,CAAC,QAAQ,YAAY;AACtC,UAAI,OAAO;AACX,UAAI,aAAa;AAEjB,aAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACvC,qBAAa,SAAS,OAAO;AAC5B,cAAI;AACH,kBAAM,WAAW,OAAO,SAAS,IAAI;AACrC,gBAAI,CAAC,SAAS,gBAAgB;AAC7B,qBAAO,MAAM;AACb,sBAAQ,QAAQ;AAChB;AAAA,YACD;AACA,gBAAI,YAAY;AACf,2BAAa;AACb,qBAAO;AAAA,YACR;AACA,gBAAI,SAAS;AACZ,oBAAM,MAAM,QAAQ,QAAQ;AAC5B,kBAAI,QAAQ,QAAW;AACtB,oBAAI,OAAO,QAAQ,YAAY,QAAQ,IAAK,QAAO,KAAK,IAAI,GAAG,KAAK,IAAI,YAAY,KAAK,MAAM,GAAG,CAAC,CAAC;AAAA,oBAC/F,OAAM,IAAI,UAAU,4DAA4D;AAAA,cACtF;AAAA,YACD;AACA,yBAAa,IAAI;AAAA,UAClB,SAAS,KAAK;AACb,mBAAO,MAAM;AACb,mBAAO,GAAG;AAAA,UACX;AAAA,QACD,CAAC;AAAA,MACF,CAAC;AAAA,IACF;AAAA;AAAA;;;AClEA;AAAA;AAAA;AACA,QAAM,EAAE,MAAM,IAAI;AAElB,WAAO,UAAU,SAAS,UAAU,SAAS;AAC5C,UAAI,WAAW,KAAM,WAAU,CAAC;AAGhC,UAAI,OAAO,YAAY,SAAU,OAAM,IAAI,UAAU,iDAAiD;AAGtG,YAAM,eAAe,cAAc,UAAU,QAAQ,WAAW;AAChE,UAAI,OAAO,iBAAiB,SAAU,OAAM,IAAI,UAAU,+CAA+C;AACzG,UAAI,CAAC,aAAc,OAAM,IAAI,UAAU,iDAAiD;AAExF,aAAO,KAAK,KAAK,EAAE,UAAU,YAAY;AAAA,IAC1C;AAAA;AAAA;;;ACfA;AAAA;AAAA;AACA,QAAM,EAAE,kBAAkB,MAAM,IAAI;AAEpC,WAAO,UAAU,SAAS,eAAe,MAAM,SAAS,IAAI;AAE3D,UAAI,WAAW,KAAM,WAAU,CAAC;AAChC,UAAI,OAAO,YAAY,YAAY;AAAE,aAAK;AAAS,kBAAU,CAAC;AAAA,MAAG;AAGjE,UAAI,OAAO,SAAS,SAAU,OAAM,IAAI,UAAU,wCAAwC;AAC1F,UAAI,OAAO,OAAO,WAAY,OAAM,IAAI,UAAU,yCAAyC;AAC3F,UAAI,OAAO,YAAY,SAAU,OAAM,IAAI,UAAU,kDAAkD;AACvG,UAAI,CAAC,KAAM,OAAM,IAAI,UAAU,sDAAsD;AAGrF,YAAM,eAAe,kBAAkB,UAAU,CAAC,iBAAiB,SAAS,cAAc,IAAI;AAC9F,YAAM,gBAAgB,iBAAiB,SAAS,eAAe;AAC/D,YAAM,aAAa,iBAAiB,SAAS,YAAY;AACzD,YAAM,UAAU,iBAAiB,SAAS,SAAS;AACnD,UAAI,WAAW;AAGf,UAAI,CAAC,SAAS;AACb,mBAAW,GAAG;AACd,YAAI,CAAC,OAAO,UAAU,QAAQ,KAAK,WAAW,EAAG,OAAM,IAAI,UAAU,mDAAmD;AACxH,YAAI,WAAW,IAAK,OAAM,IAAI,WAAW,4DAA4D;AAAA,MACtG;AAEA,WAAK,KAAK,EAAE,SAAS,IAAI,MAAM,UAAU,cAAc,eAAe,UAAU;AAChF,aAAO;AAAA,IACR;AAAA;AAAA;;;AC9BA;AAAA;AAAA;AACA,QAAM,EAAE,kBAAkB,MAAM,IAAI;AAEpC,WAAO,UAAU,SAAS,gBAAgB,MAAM,SAAS;AAExD,UAAI,OAAO,SAAS,SAAU,OAAM,IAAI,UAAU,wCAAwC;AAC1F,UAAI,OAAO,YAAY,YAAY,YAAY,KAAM,OAAM,IAAI,UAAU,kDAAkD;AAC3H,UAAI,CAAC,KAAM,OAAM,IAAI,UAAU,sDAAsD;AAGrF,YAAM,QAAQ,WAAW,UAAU,QAAQ,QAAQ;AACnD,YAAM,OAAO,kBAAkB,SAAS,QAAQ,IAAI;AACpD,YAAM,UAAU,kBAAkB,SAAS,WAAW,KAAK;AAC3D,YAAM,SAAS,kBAAkB,SAAS,UAAU,KAAK;AACzD,YAAM,eAAe,kBAAkB,UAAU,CAAC,iBAAiB,SAAS,cAAc,IAAI;AAC9F,YAAM,gBAAgB,iBAAiB,SAAS,eAAe;AAC/D,YAAM,aAAa,iBAAiB,SAAS,YAAY;AACzD,YAAM,UAAU,iBAAiB,SAAS,SAAS;AACnD,UAAI,WAAW;AAGf,UAAI,CAAC,SAAS;AACb,mBAAW,KAAK,IAAI,UAAU,IAAI,GAAG,UAAU,UAAU,OAAO,IAAI,CAAC;AACrE,YAAI,WAAW,EAAG,aAAY;AAC9B,YAAI,WAAW,IAAK,OAAM,IAAI,WAAW,4DAA4D;AAAA,MACtG;AAEA,WAAK,KAAK,EAAE,UAAU,OAAO,MAAM,SAAS,QAAQ,MAAM,UAAU,cAAc,eAAe,UAAU;AAC3G,aAAO;AAAA,IACR;AAEA,QAAM,oBAAoB,CAAC,SAAS,KAAK,aAAa;AACrD,YAAM,QAAQ,OAAO,UAAU,QAAQ,GAAG,IAAI;AAC9C,UAAI,OAAO,UAAU,WAAY,QAAO;AACxC,UAAI,SAAS,KAAM,OAAM,IAAI,UAAU,iBAAiB,GAAG,2BAA2B;AACtF,UAAI,SAAU,OAAM,IAAI,UAAU,4BAA4B,GAAG,GAAG;AACpE,aAAO;AAAA,IACR;AAEA,QAAM,YAAY,CAAC,EAAE,OAAO,MAAM;AACjC,UAAI,OAAO,UAAU,MAAM,KAAK,UAAU,EAAG,QAAO;AACpD,YAAM,IAAI,UAAU,mDAAmD;AAAA,IACxE;AAAA;AAAA;;;AC1CA;AAAA;AAAA;AACA,QAAM,EAAE,MAAM,IAAI;AAElB,WAAO,UAAU,SAAS,YAAY,MAAM,SAAS;AAEpD,UAAI,OAAO,SAAS,SAAU,OAAM,IAAI,UAAU,wCAAwC;AAC1F,UAAI,CAAC,KAAM,OAAM,IAAI,UAAU,qDAAqD;AAGpF,UAAI,YAAY;AAChB,UAAI,OAAO,YAAY,YAAY,YAAY,MAAM;AACpD,oBAAY;AACZ,kBAAU,MAAM,qBAAqB,SAAS,QAAQ,IAAI,CAAC;AAAA,MAC5D,OAAO;AACN,YAAI,OAAO,YAAY,WAAY,OAAM,IAAI,UAAU,wEAAwE;AAC/H,kBAAU,YAAY,OAAO;AAAA,MAC9B;AAEA,WAAK,KAAK,EAAE,MAAM,SAAS,MAAM,SAAS;AAC1C,aAAO;AAAA,IACR;AAEA,aAAS,YAAY,SAAS;AAC7B,aAAO,SAAS,oBAAoB,YAAY,cAAc,cAAc,MAAM;AACjF,cAAM,aAAa;AAAA,UAClB,QAAQ;AAAA,UACR,UAAU;AAAA,UACV,OAAO;AAAA,QACR;AAGA,cAAM,MAAM,MAAM,KAAK,SAAS,YAAY,IAAI;AAChD,YAAI,OAAO,QAAQ,YAAY,QAAQ,MAAM;AAC5C,gBAAM,IAAI,UAAU,yBAAyB,UAAU,4CAA4C;AAAA,QACpG;AAEA,eAAO,qBAAqB,KAAK,YAAY,UAAU;AAAA,MACxD;AAAA,IACD;AAEA,aAAS,qBAAqB,KAAK,MAAM,YAAY;AAEpD,UAAI,CAAC,eAAe,KAAK,KAAK,MAAM,GAAG;AACtC,cAAM,IAAI,UAAU,yBAAyB,UAAU,KAAK,IAAI,+CAA+C;AAAA,MAChH;AACA,UAAI,CAAC,eAAe,KAAK,KAAK,SAAS,GAAG;AACzC,cAAM,IAAI,UAAU,yBAAyB,UAAU,KAAK,IAAI,kDAAkD;AAAA,MACnH;AAGA,YAAM,OAAO,IAAI;AACjB,UAAI,OAAO,SAAS,cAAc,OAAO,eAAe,IAAI,MAAM,4BAA4B;AAC7F,cAAM,IAAI,UAAU,yBAAyB,UAAU,KAAK,IAAI,sFAAsF;AAAA,MACvJ;AAGA,UAAI,UAAU,IAAI;AAClB,UAAI,CAAC,MAAM,QAAQ,OAAO,KAAK,EAAE,UAAU,CAAC,GAAG,OAAO,GAAG,MAAM,OAAK,OAAO,MAAM,QAAQ,GAAG;AAC3F,cAAM,IAAI,UAAU,yBAAyB,UAAU,KAAK,IAAI,wFAAwF;AAAA,MACzJ;AACA,UAAI,QAAQ,WAAW,IAAI,IAAI,OAAO,EAAE,MAAM;AAC7C,cAAM,IAAI,UAAU,yBAAyB,UAAU,KAAK,IAAI,iDAAiD;AAAA,MAClH;AACA,UAAI,CAAC,QAAQ,QAAQ;AACpB,cAAM,IAAI,WAAW,yBAAyB,UAAU,KAAK,IAAI,uCAAuC;AAAA,MACzG;AAGA,UAAI;AACJ,UAAI,eAAe,KAAK,KAAK,YAAY,GAAG;AAC3C,qBAAa,IAAI;AACjB,YAAI,CAAC,MAAM,QAAQ,UAAU,KAAK,EAAE,aAAa,CAAC,GAAG,UAAU,GAAG,MAAM,OAAK,OAAO,MAAM,QAAQ,GAAG;AACpG,gBAAM,IAAI,UAAU,yBAAyB,UAAU,KAAK,IAAI,2FAA2F;AAAA,QAC5J;AAAA,MACD,OAAO;AACN,qBAAa,gBAAgB,IAAI;AAAA,MAClC;AACA,UAAI,WAAW,WAAW,IAAI,IAAI,UAAU,EAAE,MAAM;AACnD,cAAM,IAAI,UAAU,yBAAyB,UAAU,KAAK,IAAI,oDAAoD;AAAA,MACrH;AACA,UAAI,WAAW,SAAS,IAAI;AAC3B,cAAM,IAAI,WAAW,yBAAyB,UAAU,KAAK,IAAI,wEAAwE;AAAA,MAC1I;AACA,iBAAW,aAAa,YAAY;AACnC,YAAI,QAAQ,SAAS,SAAS,GAAG;AAChC,gBAAM,IAAI,UAAU,yBAAyB,UAAU,KAAK,IAAI,oCAAoC,SAAS,gEAAgE;AAAA,QAC9K;AAAA,MACD;AAGA,UAAI,eAAe;AACnB,UAAI,eAAe,KAAK,KAAK,cAAc,GAAG;AAC7C,cAAM,OAAO,IAAI;AACjB,YAAI,OAAO,SAAS,WAAW;AAC9B,gBAAM,IAAI,UAAU,yBAAyB,UAAU,KAAK,IAAI,mFAAmF;AAAA,QACpJ;AACA,uBAAe,CAAC;AAAA,MACjB;AAGA,UAAI,aAAa;AACjB,UAAI,eAAe,KAAK,KAAK,YAAY,GAAG;AAC3C,qBAAa,IAAI;AACjB,YAAI,OAAO,eAAe,WAAW;AACpC,gBAAM,IAAI,UAAU,yBAAyB,UAAU,KAAK,IAAI,iFAAiF;AAAA,QAClJ;AAAA,MACD;AAGA,YAAM,oBAAoB;AAAA,QACzB,GAAG,WAAW,IAAI,UAAU,EAAE,IAAI,SAAO,GAAG,GAAG,SAAS;AAAA,QACxD,GAAG,QAAQ,IAAI,UAAU;AAAA,MAC1B;AACA,aAAO;AAAA,QACN,kBAAkB,kBAAkB,KAAK,IAAI,CAAC;AAAA,QAC9C,cAAc,MAAM,IAAI,IAAI,QAAQ,IAAI,CAAC,GAAG,MAAM,CAAC,GAAG,WAAW,SAAS,CAAC,CAAC,CAAC,GAAG,UAAU;AAAA,QAC1F;AAAA,QACA;AAAA,QACA;AAAA,MACD;AAAA,IACD;AAEA,aAAS,cAAc,WAAW,WAAW,YAAY;AACxD,aAAO,UAAU,gBAAgB,MAAM;AAOtC,cAAM,SAAS,KAAK,IAAI,OAAK,OAAO,SAAS,CAAC,IAAI,OAAO,KAAK,CAAC,IAAI,CAAC;AACpE,iBAAS,IAAI,GAAG,IAAI,UAAU,MAAM,EAAE,GAAG;AACxC,iBAAO,KAAK,IAAI;AAAA,QACjB;AACA,mBAAW,OAAO,UAAU,GAAG,IAAI,GAAG;AACrC,cAAI,MAAM,QAAQ,GAAG,GAAG;AACvB,4BAAgB,KAAK,QAAQ,UAAU,MAAM,UAAU;AACvD,kBAAM;AAAA,UACP,WAAW,OAAO,QAAQ,YAAY,QAAQ,MAAM;AACnD,6BAAiB,KAAK,QAAQ,WAAW,UAAU;AACnD,kBAAM;AAAA,UACP,OAAO;AACN,kBAAM,IAAI,UAAU,yBAAyB,UAAU,mDAAmD;AAAA,UAC3G;AAAA,QACD;AAAA,MACD;AAAA,IACD;AAEA,aAAS,gBAAgB,KAAK,QAAQ,aAAa,YAAY;AAC9D,UAAI,IAAI,WAAW,aAAa;AAC/B,cAAM,IAAI,UAAU,yBAAyB,UAAU,qDAAqD;AAAA,MAC7G;AACA,YAAM,SAAS,OAAO,SAAS;AAC/B,eAAS,IAAI,GAAG,IAAI,aAAa,EAAE,GAAG;AACrC,eAAO,IAAI,MAAM,IAAI,IAAI,CAAC;AAAA,MAC3B;AAAA,IACD;AAEA,aAAS,iBAAiB,KAAK,QAAQ,WAAW,YAAY;AAC7D,UAAI,QAAQ;AACZ,iBAAW,OAAO,OAAO,KAAK,GAAG,GAAG;AACnC,cAAM,QAAQ,UAAU,IAAI,GAAG;AAC/B,YAAI,UAAU,QAAW;AACxB,gBAAM,IAAI,UAAU,yBAAyB,UAAU,8CAA8C,GAAG,GAAG;AAAA,QAC5G;AACA,eAAO,KAAK,IAAI,IAAI,GAAG;AACvB,iBAAS;AAAA,MACV;AACA,UAAI,UAAU,UAAU,MAAM;AAC7B,cAAM,IAAI,UAAU,yBAAyB,UAAU,sCAAsC;AAAA,MAC9F;AAAA,IACD;AAEA,aAAS,gBAAgB,EAAE,OAAO,GAAG;AACpC,UAAI,CAAC,OAAO,UAAU,MAAM,KAAK,SAAS,GAAG;AAC5C,cAAM,IAAI,UAAU,mDAAmD;AAAA,MACxE;AACA,YAAM,SAAS,CAAC;AAChB,eAAS,IAAI,GAAG,IAAI,QAAQ,EAAE,GAAG;AAChC,eAAO,KAAK,IAAI,IAAI,CAAC,EAAE;AAAA,MACxB;AACA,aAAO;AAAA,IACR;AAEA,QAAM,EAAE,eAAe,IAAI,OAAO;AAClC,QAAM,EAAE,MAAM,IAAI,SAAS;AAC3B,QAAM,6BAA6B,OAAO,eAAe,aAAW;AAAA,IAAC,CAAC;AACtE,QAAM,aAAa,SAAO,IAAI,IAAI,QAAQ,MAAM,IAAI,CAAC;AACrD,QAAM,QAAQ,OAAK,MAAM;AAAA;AAAA;;;AC5LzB;AAAA;AAAA;AACA,QAAM,qBAAqB,SAAS,WAAW;AAAA,IAAC;AAEhD,WAAO,UAAU,SAAS,QAAQ,OAAO,MAAM;AAC9C,aAAO,OAAO,OAAO,IAAI,mBAAmB,GAAG,IAAI;AAAA,IACpD;AAAA;AAAA;;;ACLA;AAAA;AAAA;AACA,QAAM,KAAK;AACX,QAAM,OAAO;AACb,QAAM,OAAO;AACb,QAAM,cAAc;AAEpB,QAAI;AAEJ,aAAS,SAAS,eAAe,SAAS;AACzC,UAAI,cAAc,MAAM;AACvB,eAAO,IAAI,SAAS,eAAe,OAAO;AAAA,MAC3C;AAGA,UAAI;AACJ,UAAI,OAAO,SAAS,aAAa,GAAG;AACnC,iBAAS;AACT,wBAAgB;AAAA,MACjB;AACA,UAAI,iBAAiB,KAAM,iBAAgB;AAC3C,UAAI,WAAW,KAAM,WAAU,CAAC;AAGhC,UAAI,OAAO,kBAAkB,SAAU,OAAM,IAAI,UAAU,wCAAwC;AACnG,UAAI,OAAO,YAAY,SAAU,OAAM,IAAI,UAAU,kDAAkD;AACvG,UAAI,cAAc,QAAS,OAAM,IAAI,UAAU,mDAAmD;AAClG,UAAI,YAAY,QAAS,OAAM,IAAI,UAAU,yEAAyE;AAGtH,YAAM,WAAW,cAAc,KAAK;AACpC,YAAM,YAAY,aAAa,MAAM,aAAa;AAClD,YAAM,WAAW,KAAK,iBAAiB,SAAS,UAAU;AAC1D,YAAM,gBAAgB,KAAK,iBAAiB,SAAS,eAAe;AACpE,YAAM,UAAU,aAAa,UAAU,QAAQ,UAAU;AACzD,YAAM,UAAU,aAAa,UAAU,QAAQ,UAAU;AACzD,YAAM,gBAAgB,mBAAmB,UAAU,QAAQ,gBAAgB;AAG3E,UAAI,YAAY,aAAa,CAAC,OAAQ,OAAM,IAAI,UAAU,kDAAkD;AAC5G,UAAI,CAAC,OAAO,UAAU,OAAO,KAAK,UAAU,EAAG,OAAM,IAAI,UAAU,wDAAwD;AAC3H,UAAI,UAAU,WAAY,OAAM,IAAI,WAAW,oDAAoD;AACnG,UAAI,WAAW,QAAQ,OAAO,YAAY,WAAY,OAAM,IAAI,UAAU,gDAAgD;AAC1H,UAAI,iBAAiB,QAAQ,OAAO,kBAAkB,YAAY,OAAO,kBAAkB,SAAU,OAAM,IAAI,UAAU,oEAAoE;AAG7L,UAAI;AACJ,UAAI,iBAAiB,MAAM;AAC1B,gBAAQ,kBAAkB,gBAAgB,mBAAoB,qBAAqB;AAAA,MACpF,WAAW,OAAO,kBAAkB,UAAU;AAE7C,cAAM,cAAc,OAAO,4BAA4B,aAAa,0BAA0B;AAC9F,gBAAQ,YAAY,KAAK,QAAQ,aAAa,EAAE,QAAQ,cAAc,OAAO,CAAC;AAAA,MAC/E,OAAO;AAEN,gBAAQ;AAAA,MACT;AAEA,UAAI,CAAC,MAAM,eAAe;AACzB,cAAM,oBAAoB,WAAW;AACrC,cAAM,gBAAgB;AAAA,MACvB;AAGA,UAAI,CAAC,aAAa,CAAC,SAAS,WAAW,OAAO,KAAK,CAAC,GAAG,WAAW,KAAK,QAAQ,QAAQ,CAAC,GAAG;AAC1F,cAAM,IAAI,UAAU,2DAA2D;AAAA,MAChF;AAEA,aAAO,iBAAiB,MAAM;AAAA,QAC7B,CAAC,KAAK,KAAK,GAAG,EAAE,OAAO,IAAI,MAAM,SAAS,UAAU,eAAe,WAAW,UAAU,eAAe,SAAS,WAAW,MAAM,UAAU,IAAI,EAAE;AAAA,QACjJ,GAAG,SAAS;AAAA,MACb,CAAC;AAAA,IACF;AAEA,QAAM,WAAW;AACjB,aAAS,UAAU,UAAU,SAAS;AACtC,aAAS,UAAU,cAAc;AACjC,aAAS,UAAU,SAAS;AAC5B,aAAS,UAAU,SAAS;AAC5B,aAAS,UAAU,YAAY;AAC/B,aAAS,UAAU,WAAW;AAC9B,aAAS,UAAU,YAAY;AAC/B,aAAS,UAAU,QAAQ;AAC3B,aAAS,UAAU,gBAAgB,SAAS;AAC5C,aAAS,UAAU,OAAO,SAAS;AACnC,aAAS,UAAU,QAAQ,SAAS;AACpC,aAAS,UAAU,sBAAsB,SAAS;AAClD,aAAS,UAAU,aAAa,SAAS;AACzC,aAAS,UAAU,KAAK,OAAO,IAAI;AAEnC,WAAO,UAAU;AAAA;AAAA;;;ACzFjB;AAAA;AACA,WAAO,UAAU;AACjB,WAAO,QAAQ,cAAc;AAAA;AAAA;", "names": ["path", "i", "require_util"]}